<?php

namespace App\Http\Controllers;

use App\Models\Item;
use Exception;
use Illuminate\Http\Request;
use App\Models\ItemPayment;
use Yajra\DataTables\Facades\DataTables;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\Http\JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
        $payments = ItemPayment::with('item', 'provider')->get();
            return DataTables::of($payments)
                ->addIndexColumn()
                ->addColumn('first_image', function (ItemPayment $item) {
                    return $item->item->images->first() ? $item->item->images->first()->url : null;
                })
                ->addColumn('item_title', function ($payment) {
                    return $payment->item->title;
                })
                ->addColumn('provider_name', function ($payment) {
                    return $payment->provider ? $payment->provider->name : 'BANKILY';
                })
                ->addColumn('action', function ($payment) {
                    return '<a href="' . route('items.show', $payment->item_id) . '" class="btn btn-primary">View</a>';
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        return view('backoffice.payments.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
