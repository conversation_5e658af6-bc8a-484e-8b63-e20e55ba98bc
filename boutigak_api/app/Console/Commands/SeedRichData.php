<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\RichDataSeeder;
use Illuminate\Support\Facades\Log;

class SeedRichData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:seed-rich {--clean : Clean database before seeding}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed the database with rich test data including users, stores, items, orders, and discussions with real images';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Rich Data Seeding...');
        $this->info('This will create:');
        $this->info('- 4 verified users');
        $this->info('- 2 stores with real images');
        $this->info('- 16 items with real product images');
        $this->info('- Orders and payment proofs');
        $this->info('- Discussions between users');
        $this->newLine();

        if ($this->option('clean')) {
            $this->warn('⚠️  This will CLEAN your database first!');
        }

        if (!$this->confirm('Do you want to continue?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        try {
            $this->info('Running rich data seeder...');
            
            // Configure logging to show in console
            Log::listen(function ($level, $message, $context) {
                if (str_contains($message, '🚀') || str_contains($message, '✅') || 
                    str_contains($message, '📍') || str_contains($message, '👥') || 
                    str_contains($message, '🏪') || str_contains($message, '📦') || 
                    str_contains($message, '💳') || str_contains($message, '💬') ||
                    str_contains($message, '🧹')) {
                    $this->line($message);
                }
            });

            $seeder = new RichDataSeeder();
            $seeder->run();

            $this->newLine();
            $this->info('✅ Rich data seeding completed successfully!');
            $this->newLine();
            $this->info('You can now test your app with:');
            $this->info('- 4 users with phone numbers: +22220123456, +22220234567, +22220345678, +22220456789');
            $this->info('- Password for all users: 12345678');
            $this->info('- 2 stores: "Boutique Ahmed Electronics" and "Fashion Fatima"');
            $this->info('- 16 items with real images');
            $this->info('- Various orders and payment proofs');
            $this->info('- Discussions between users');

        } catch (\Exception $e) {
            $this->error('❌ Error during seeding: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
