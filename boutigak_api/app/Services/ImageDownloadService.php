<?php

namespace App\services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\services\ImageOptimizationService;

class ImageDownloadService
{
    protected $imageOptimizationService;

    public function __construct(ImageOptimizationService $imageOptimizationService)
    {
        $this->imageOptimizationService = $imageOptimizationService;
    }

    /**
     * Download image from URL and process it through ImageOptimizationService
     *
     * @param string $url
     * @param string $directory
     * @return array Returns ['path' => storage_path, 'url' => public_url] from ImageOptimizationService
     */
    public function downloadAndOptimize(string $url, string $directory = 'images'): array
    {
        try {
            Log::info("Downloading image from: {$url}");

            // Download the image
            $response = Http::timeout(30)->get($url);

            if (!$response->successful()) {
                throw new \Exception("Failed to download image from {$url}");
            }

            // Get the image content
            $imageContent = $response->body();

            // Determine file extension from content type or URL
            $contentType = $response->header('Content-Type');
            $extension = $this->getExtensionFromContentType($contentType) ?: 'jpg';

            // Create a temporary file
            $tempFileName = 'temp_' . uniqid() . '.' . $extension;
            $tempPath = sys_get_temp_dir() . '/' . $tempFileName;

            // Save content to temporary file
            file_put_contents($tempPath, $imageContent);

            // Create UploadedFile instance
            $uploadedFile = new UploadedFile(
                $tempPath,
                $tempFileName,
                $contentType,
                null,
                true // test mode - don't validate file
            );

            // Process through ImageOptimizationService - this returns the URL we need
            $optimizationResult = $this->imageOptimizationService->optimizeAndStore(
                $uploadedFile,
                $directory
            );

            // Clean up temporary file
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }

            Log::info("Successfully processed image through ImageOptimizationService", [
                'original_url' => $url,
                'optimized_path' => $optimizationResult['path'],
                'final_url' => $optimizationResult['url']
            ]);

            // Return the result from ImageOptimizationService which contains the final URL
            return $optimizationResult;

        } catch (\Exception $e) {
            Log::error("Error downloading/processing image from {$url}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get file extension from content type
     */
    private function getExtensionFromContentType(?string $contentType): ?string
    {
        $mimeToExtension = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/svg+xml' => 'svg',
        ];

        return $mimeToExtension[$contentType] ?? null;
    }

    /**
     * Get random product images from Picsum (Lorem Picsum)
     */
    public function getRandomProductImages(int $count = 4): array
    {
        $images = [];
        for ($i = 0; $i < $count; $i++) {
            $width = rand(400, 800);
            $height = rand(400, 800);
            $seed = rand(1, 1000);
            // Using Lorem Picsum as alternative to Unsplash
            $images[] = "https://picsum.photos/{$width}/{$height}?random={$seed}";
        }

        return $images;
    }

    /**
     * Get random store images
     */
    public function getRandomStoreImages(int $count = 3): array
    {
        $images = [];
        for ($i = 0; $i < $count; $i++) {
            $width = rand(600, 1000);
            $height = rand(400, 600);
            $seed = rand(1001, 2000);
            // Using Lorem Picsum as alternative to Unsplash
            $images[] = "https://picsum.photos/{$width}/{$height}?random={$seed}";
        }

        return $images;
    }

    /**
     * Get payment screenshot mockup
     */
    public function getPaymentScreenshot(): string
    {
        $seed = rand(2001, 3000);
        // Using Lorem Picsum for payment screenshots
        return "https://picsum.photos/400/600?random={$seed}";
    }

    /**
     * Download multiple images and return their data
     * Each result contains ['path' => storage_path, 'url' => final_optimized_url]
     */
    public function downloadMultipleImages(array $urls, string $directory): array
    {
        $results = [];

        foreach ($urls as $index => $url) {
            try {
                // Download and optimize through ImageOptimizationService
                $optimizedResult = $this->downloadAndOptimize($url, $directory);
                $results[] = $optimizedResult;

                Log::info("Successfully processed image {$index}", [
                    'original_url' => $url,
                    'final_url' => $optimizedResult['url'],
                    'storage_path' => $optimizedResult['path']
                ]);

                // Add small delay to avoid overwhelming the server
                usleep(500000); // 0.5 seconds

            } catch (\Exception $e) {
                Log::warning("Failed to download and optimize image {$index} from {$url}: " . $e->getMessage());
                // Continue with other images
            }
        }

        Log::info("Completed downloading images", [
            'successful' => count($results),
            'total' => count($urls),
            'directory' => $directory
        ]);

        return $results;
    }
}
