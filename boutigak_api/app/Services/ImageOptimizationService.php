<?php

namespace App\services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageOptimizationService
{
    private ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Optimize and store an uploaded image
     *
     * @param UploadedFile $image
     * @param string $directory Directory within storage/app/public/
     * @param int $maxWidth Maximum width (default: 1920)
     * @param int $maxHeight Maximum height (default: 1080)
     * @param int $quality Quality percentage (default: 85)
     * @return array Returns ['path' => storage_path, 'url' => public_url]
     */
    public function optimizeAndStore(
        UploadedFile $image,
        string $directory = 'images',
        int $maxWidth = 1920,
        int $maxHeight = 1080,
        int $quality = 60
    ): array {
        // Generate unique filename with .webp extension
        $filename = time() . '_' . uniqid() . '.webp';
        $storagePath = 'public/' . trim($directory, '/') . '/' . $filename;

        // Check original image size (150KB = 153600 bytes)
        $originalSize = $image->getSize();
        
        // Read the image
        $imageInstance = $this->imageManager->read($image);
        
        // If original size is <= 150KB, just convert to webp without optimization
        if ($originalSize <= 153600) {
            $optimizedImage = $imageInstance->encodeByExtension('webp', quality: 100);
        } else {
            // If larger than 150KB, optimize with specified quality
            $optimizedImage = $imageInstance->encodeByExtension('webp', quality: $quality);
        }

        // log image details
        \Log::info('Image processing details', [
            'original_size' => $originalSize,
            'optimized_size' => strlen($optimizedImage->toString()),
            'quality_used' => $originalSize <= 153600 ? 100 : $quality
        ]);

        


        // Store the optimized image on SFTP
        $stored = Storage::disk('sftp')->put($storagePath, $optimizedImage->toString());

        \Log::info('SFTP store result', [
            'path' => $storagePath,
            'stored' => $stored
        ]);


        $sftpBaseUrl = config('assets.sftp.base_url', 'https://storage.boutigak.com ');

        return [
            'path' => $storagePath,
            'url' => $sftpBaseUrl . '/' . $storagePath
        ];
    }

    /**
     * Optimize image for thumbnails (smaller size)
     *
     * @param UploadedFile $image
     * @param string $directory
     * @param int $maxWidth
     * @param int $maxHeight
     * @param int $quality
     * @return array
     */
    public function optimizeForThumbnail(
        UploadedFile $image,
        string $directory = 'thumbnails',
        int $maxWidth = 400,
        int $maxHeight = 400,
        int $quality = 80
    ): array {
        return $this->optimizeAndStore($image, $directory, $maxWidth, $maxHeight, $quality);
    }

    /**
     * Optimize image for high quality display
     *
     * @param UploadedFile $image
     * @param string $directory
     * @param int $maxWidth
     * @param int $maxHeight
     * @param int $quality
     * @return array
     */
    public function optimizeForHighQuality(
        UploadedFile $image,
        string $directory = 'images',
        int $maxWidth = 2560,
        int $maxHeight = 1440,
        int $quality = 90
    ): array {
        return $this->optimizeAndStore($image, $directory, $maxWidth, $maxHeight, $quality);
    }

    /**
     * Get optimal dimensions while maintaining aspect ratio
     *
     * @param int $originalWidth
     * @param int $originalHeight
     * @param int $maxWidth
     * @param int $maxHeight
     * @return array
     */
    public function getOptimalDimensions(
        int $originalWidth,
        int $originalHeight,
        int $maxWidth,
        int $maxHeight
    ): array {
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);

        if ($ratio >= 1) {
            // Image is smaller than max dimensions, keep original size
            return ['width' => $originalWidth, 'height' => $originalHeight];
        }

        return [
            'width' => (int)($originalWidth * $ratio),
            'height' => (int)($originalHeight * $ratio)
        ];
    }
}
