<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Store;
use App\Models\Item;
use App\Models\Order;
use App\Models\PaymentProof;
use App\Models\Discution;
use App\Models\DiscutionMessage;
use App\Models\Media;
use App\Models\ItemMedia;
use App\Models\StoreMedia;
use App\Models\Category;
use App\Models\Brand;
use App\Models\StoreType;
use App\Models\Location;
use App\Models\EPaymentProvider;
use App\Services\ImageDownloadService;
use Carbon\Carbon;

class RichDataSeeder extends Seeder
{
    protected $imageDownloadService;

    public function __construct()
    {
        $this->imageDownloadService = app(ImageDownloadService::class);
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Log::info('🚀 Starting Rich Data Seeding...');

        // Clean database first
        $this->cleanDatabase();

        // Create locations first
        $this->createLocations();

        // Create users
        $users = $this->createUsers();

        // Create stores for 2 users
        $stores = $this->createStores($users);

        // Create items for all users
        $this->createItems($users, $stores);

        // Create orders and payments
        $this->createOrdersAndPayments($users, $stores);

        // Create discussions
        $this->createDiscussions($users);

        Log::info('✅ Rich Data Seeding completed successfully!');
    }

    /**
     * Clean the database
     */
    private function cleanDatabase(): void
    {
        Log::info('🧹 Cleaning database...');

        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clean tables in correct order
        $tables = [
            'discution_message',
            'discution',
            'payment_proofs',
            'order_payment',
            'order',
            'item_media',
            'store_media',
            'item',
            'store',
            'media',
            'user_locations',
            'users',
            'location'
        ];

        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table)) {
                DB::table($table)->truncate();
                Log::info("Cleaned table: {$table}");
            }
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    /**
     * Create locations
     */
    private function createLocations(): array
    {
        Log::info('📍 Creating locations...');

        $locations = [
            ['name' => 'Nouakchott Centre', 'address' => 'Centre ville, Nouakchott', 'latitude' => 18.0735, 'longitude' => -15.9582],
            ['name' => 'Tevragh Zeina', 'address' => 'Tevragh Zeina, Nouakchott', 'latitude' => 18.0892, 'longitude' => -15.9777],
            ['name' => 'Ksar', 'address' => 'Ksar, Nouakchott', 'latitude' => 18.0669, 'longitude' => -15.9464],
            ['name' => 'Sebkha', 'address' => 'Sebkha, Nouakchott', 'latitude' => 18.1006, 'longitude' => -15.9572],
        ];

        $createdLocations = [];
        foreach ($locations as $locationData) {
            $location = Location::create($locationData);
            $createdLocations[] = $location;
            Log::info("Created location: {$location->name}");
        }

        return $createdLocations;
    }

    /**
     * Create users
     */
    private function createUsers(): array
    {
        Log::info('👥 Creating users...');

        $userData = [
            [
                'firstname' => 'Ahmed',
                'lastname' => 'Moloude',
                'phone' => '38184156',
                'gender' => 'male',
                'lang' => 'fr',
                'has_store' => true
            ],
            [
                'firstname' => 'Mohamed',
                'lastname' => 'AbdelKade',
                'phone' => '36666688',
                'gender' => 'male',
                'lang' => 'fr',
                'has_store' => true
            ],
            [
                'firstname' => 'Omar',
                'lastname' => 'Hassan',
                'phone' => '20345678',
                'gender' => 'male',
                'lang' => 'en',
                'has_store' => false
            ],
            [
                'firstname' => 'Aicha',
                'lastname' => 'Mint Salem',
                'phone' => '20456789',
                'gender' => 'female',
                'lang' => 'ar',
                'has_store' => false
            ]
        ];

        $users = [];
        $locations = Location::all();

        foreach ($userData as $index => $data) {
            $user = User::create([
                'firstname' => $data['firstname'],
                'lastname' => $data['lastname'],
                'phone' => $data['phone'],
                'password' => Hash::make('12345678'),
                'gender' => $data['gender'],
                'lang' => $data['lang'],
                'has_store' => $data['has_store'],
                'is_verified' => true,
                'invitationcode' => 'INV' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'location_id' => $locations->random()->id,
                'last_activity' => Carbon::now()->subHours(rand(1, 24)),
                'is_deleted' => false
            ]);

            $users[] = $user;
            Log::info("Created user: {$user->firstname} {$user->lastname}");
        }

        return $users;
    }

    /**
     * Create stores for users with has_store = true
     */
    private function createStores(array $users): array
    {
        Log::info('🏪 Creating stores...');

        $stores = [];
        $storeTypes = StoreType::all();
        $locations = Location::all();

        foreach ($users as $user) {
            if ($user->has_store) {
                $storeData = [
                    'Ahmed' => [
                        'name' => 'Boutique Ahmed Electronics',
                        'description' => 'Magasin d\'électronique et d\'accessoires high-tech',
                        'opening_time' => '08:00',
                        'closing_time' => '20:00'
                    ],
                    'Fatima' => [
                        'name' => 'Fashion Fatima',
                        'description' => 'Boutique de mode féminine et accessoires',
                        'opening_time' => '09:00',
                        'closing_time' => '19:00'
                    ]
                ];

                $data = $storeData[$user->firstname];
                
                $store = Store::create([
                    'name' => $data['name'],
                    'description' => $data['description'],
                    'user_id' => $user->id,
                    'type_id' => $storeTypes->random()->id,
                    'location_id' => $locations->random()->id,
                    'opening_time' => $data['opening_time'],
                    'closing_time' => $data['closing_time'],
                    'is_promoted' => rand(0, 1),
                    'promotion_position' => rand(1, 5),
                    'is_open' => true
                ]);

                // Add store images
                $this->addStoreImages($store);
                
                $stores[] = $store;
                Log::info("Created store: {$store->name}");
            }
        }

        return $stores;
    }

    /**
     * Add images to store
     */
    private function addStoreImages(Store $store): void
    {
        try {
            Log::info("Adding images to store: {$store->name}");

            $imageUrls = $this->imageDownloadService->getRandomStoreImages(3);
            $imageResults = $this->imageDownloadService->downloadMultipleImages($imageUrls, 'store_images');

            foreach ($imageResults as $imageData) {
                $media = Media::create([
                    'type' => 'image/webp',
                    'url' => $imageData['url'],
                    'path' => $imageData['path']
                ]);

                StoreMedia::create([
                    'store_id' => $store->id,
                    'media_id' => $media->id
                ]);
            }

            Log::info("Added " . count($imageResults) . " images to store: {$store->name}");

        } catch (\Exception $e) {
            Log::error("Failed to add images to store {$store->name}: " . $e->getMessage());
        }
    }

    /**
     * Create items for all users
     */
    private function createItems(array $users, array $stores): void
    {
        Log::info('📦 Creating items...');

        $categories = Category::whereNotNull('parent_id')->get(); // Get leaf categories
        $brands = Brand::all();

        $itemTemplates = [
            [
                'title' => 'iPhone 14 Pro Max',
                'title_ar' => 'آيفون 14 برو ماكس',
                'description' => 'Latest iPhone with advanced camera system and A16 Bionic chip',
                'description_ar' => 'أحدث آيفون مع نظام كاميرا متقدم ومعالج A16 بايونيك',
                'price' => 1200.00,
                'condition' => 'New'
            ],
            [
                'title' => 'Samsung Galaxy S23',
                'title_ar' => 'سامسونج جالاكسي S23',
                'description' => 'Flagship Android phone with excellent display and camera',
                'description_ar' => 'هاتف أندرويد رائد مع شاشة وكاميرا ممتازة',
                'price' => 900.00,
                'condition' => 'New'
            ],
            [
                'title' => 'MacBook Air M2',
                'title_ar' => 'ماك بوك إير M2',
                'description' => 'Lightweight laptop with M2 chip for professional work',
                'description_ar' => 'لابتوب خفيف مع معالج M2 للعمل المهني',
                'price' => 1500.00,
                'condition' => 'New'
            ],
            [
                'title' => 'Nike Air Jordan',
                'title_ar' => 'نايك إير جوردان',
                'description' => 'Classic basketball shoes in excellent condition',
                'description_ar' => 'أحذية كرة سلة كلاسيكية في حالة ممتازة',
                'price' => 180.00,
                'condition' => 'Used'
            ],
            [
                'title' => 'Designer Handbag',
                'title_ar' => 'حقيبة يد مصممة',
                'description' => 'Luxury leather handbag from premium brand',
                'description_ar' => 'حقيبة يد جلدية فاخرة من علامة تجارية راقية',
                'price' => 450.00,
                'condition' => 'New'
            ],
            [
                'title' => 'Gaming Chair',
                'title_ar' => 'كرسي ألعاب',
                'description' => 'Ergonomic gaming chair with RGB lighting',
                'description_ar' => 'كرسي ألعاب مريح مع إضاءة RGB',
                'price' => 320.00,
                'condition' => 'New'
            ],
            [
                'title' => 'Vintage Watch',
                'title_ar' => 'ساعة عتيقة',
                'description' => 'Classic vintage watch in working condition',
                'description_ar' => 'ساعة عتيقة كلاسيكية في حالة عمل',
                'price' => 280.00,
                'condition' => 'Used'
            ],
            [
                'title' => 'Wireless Headphones',
                'title_ar' => 'سماعات لاسلكية',
                'description' => 'Premium wireless headphones with noise cancellation',
                'description_ar' => 'سماعات لاسلكية راقية مع إلغاء الضوضاء',
                'price' => 250.00,
                'condition' => 'New'
            ]
        ];

        $itemIndex = 0;
        foreach ($users as $user) {
            for ($i = 0; $i < 4; $i++) {
                $template = $itemTemplates[$itemIndex % count($itemTemplates)];

                // Assign store if user has one
                $storeId = null;
                if ($user->has_store) {
                    $userStore = collect($stores)->firstWhere('user_id', $user->id);
                    $storeId = $userStore?->id;
                }

                $item = Item::create([
                    'title' => $template['title'] . " #{$i}",
                    'title_ar' => $template['title_ar'] . " #{$i}",
                    'description' => $template['description'],
                    'description_ar' => $template['description_ar'],
                    'price' => $template['price'] + rand(-50, 100),
                    'condition' => $template['condition'],
                    'quantity' => rand(1, 10),
                    'brand_id' => $brands->random()->id,
                    'category_id' => $categories->random()->id,
                    'user_id' => $user->id,
                    'store_id' => $storeId,
                    'status' => 'active',
                    'sold_out' => false,
                    'is_promoted' => rand(0, 1),
                    'has_promotion' => rand(0, 1),
                    'promotion_percentage' => rand(0, 1) ? rand(5, 30) : 0
                ]);

                // Add item images
                $this->addItemImages($item);

                Log::info("Created item: {$item->title} for user: {$user->firstname}");
                $itemIndex++;
            }
        }
    }

    /**
     * Add images to item
     */
    private function addItemImages(Item $item): void
    {
        try {
            Log::info("Adding images to item: {$item->title}");

            $imageUrls = $this->imageDownloadService->getRandomProductImages(rand(2, 4));
            $imageResults = $this->imageDownloadService->downloadMultipleImages($imageUrls, 'item_images');

            foreach ($imageResults as $index => $imageData) {
                $media = Media::create([
                    'type' => 'image/webp',
                    'url' => $imageData['url'],
                    'path' => $imageData['path']
                ]);

                ItemMedia::create([
                    'item_id' => $item->id,
                    'media_id' => $media->id,
                    'order' => $index
                ]);
            }

            Log::info("Added " . count($imageResults) . " images to item: {$item->title}");

        } catch (\Exception $e) {
            Log::error("Failed to add images to item {$item->title}: " . $e->getMessage());
        }
    }

    /**
     * Create orders and payments
     */
    private function createOrdersAndPayments(array $users, array $stores): void
    {
        Log::info('💳 Creating orders and payments...');

        $items = Item::all();
        $paymentProviders = EPaymentProvider::all();

        // Create 8-10 orders
        for ($i = 0; $i < rand(8, 10); $i++) {
            $buyer = $users[array_rand($users)];
            $item = $items->random();
            $seller = $item->user;

            // Don't let users buy from themselves
            if ($buyer->id === $seller->id) {
                continue;
            }

            $order = Order::create([
                'user_id' => $buyer->id,
                'store_id' => $item->store_id,
                'item_id' => $item->id,
                'status' => ['pending', 'confirmed', 'delivered', 'cancelled'][rand(0, 3)],
                'total' => $item->price + rand(5, 20), // Add delivery charge
                'delivery_charge' => rand(5, 20),
                'is_paid' => rand(0, 1),
                'is_cash_on_delivery' => rand(0, 1),
                'location_id' => $buyer->location_id
            ]);

            // Create payment proof if order is paid
            if ($order->is_paid && !$order->is_cash_on_delivery) {
                $this->createPaymentProof($order, $paymentProviders->random());
            }

            Log::info("Created order #{$order->id} - {$buyer->firstname} buying {$item->title}");
        }
    }

    /**
     * Create payment proof with screenshot
     */
    private function createPaymentProof(Order $order, EPaymentProvider $provider): void
    {
        try {
            Log::info("Creating payment proof for order #{$order->id}");

            $screenshotUrl = $this->imageDownloadService->getPaymentScreenshot();
            $imageData = $this->imageDownloadService->downloadAndOptimize($screenshotUrl, 'payment_proofs');

            PaymentProof::create([
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'item_id' => $order->item_id,
                'store_id' => $order->store_id,
                'provider_id' => $provider->id,
                'screenshot' => $imageData['url'],
                'amount' => $order->total,
                'reference_number' => 'REF' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT),
                'status' => ['pending', 'verified', 'rejected'][rand(0, 2)],
                'to_store' => $order->store_id ? true : false
            ]);

            Log::info("Created payment proof for order #{$order->id}");

        } catch (\Exception $e) {
            Log::error("Failed to create payment proof for order #{$order->id}: " . $e->getMessage());
        }
    }

    /**
     * Create discussions between users
     */
    private function createDiscussions(array $users): void
    {
        Log::info('💬 Creating discussions...');

        $items = Item::all();

        // Create 5-8 discussions
        for ($i = 0; $i < rand(5, 8); $i++) {
            $buyer = $users[array_rand($users)];
            $item = $items->random();
            $seller = $item->user;

            // Don't let users discuss with themselves
            if ($buyer->id === $seller->id) {
                continue;
            }

            $discussion = Discution::create([
                'item_id' => $item->id,
                'buyer_id' => $buyer->id,
                'seller_id' => $seller->id,
                'store_id' => $item->store_id,
                'is_store_discussion' => $item->store_id ? true : false
            ]);

            // Add messages to discussion
            $this->addDiscussionMessages($discussion, $buyer, $seller, $item);

            Log::info("Created discussion between {$buyer->firstname} and {$seller->firstname} about {$item->title}");
        }
    }

    /**
     * Add messages to discussion
     */
    private function addDiscussionMessages(Discution $discussion, User $buyer, User $seller, Item $item): void
    {
        $messages = [
            [
                'sender_id' => $buyer->id,
                'content' => "Hello! I'm interested in your {$item->title}. Is it still available?",
                'is_an_offer' => false
            ],
            [
                'sender_id' => $seller->id,
                'content' => "Yes, it's still available! It's in excellent condition.",
                'is_an_offer' => false
            ],
            [
                'sender_id' => $buyer->id,
                'content' => "Would you accept " . ($item->price - 50) . " for it?",
                'is_an_offer' => true,
                'price' => $item->price - 50
            ],
            [
                'sender_id' => $seller->id,
                'content' => "I can do " . ($item->price - 25) . ". That's my best price.",
                'is_an_offer' => true,
                'price' => $item->price - 25
            ],
            [
                'sender_id' => $buyer->id,
                'content' => "Deal! When can I pick it up?",
                'is_an_offer' => false
            ]
        ];

        foreach ($messages as $index => $messageData) {
            DiscutionMessage::create([
                'discution_id' => $discussion->id,
                'sender_id' => $messageData['sender_id'],
                'content' => $messageData['content'],
                'is_an_offer' => $messageData['is_an_offer'],
                'price' => $messageData['price'] ?? null,
                'is_store_discussion' => $discussion->is_store_discussion,
                'created_at' => Carbon::now()->subMinutes((count($messages) - $index) * 10)
            ]);
        }
    }
}
