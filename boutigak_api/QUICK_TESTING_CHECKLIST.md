# 🚀 Quick Testing Checklist for Boutigak App

## 📱 **Setup & Login (5 minutes)**

### Test Users (Updated Phone Numbers):
- **<PERSON>** (Store Owner): `20123456` / `12345678` 
- **<PERSON><PERSON>** (Store Owner): `20234567` / `12345678`
- **<PERSON>** (Regular User): `20345678` / `12345678`
- **<PERSON>cha** (Regular User): `20456789` / `12345678`

### Quick Setup:
```bash
php artisan db:seed-rich --clean
```

---

## ⚡ **5-Minute Quick Test**

### 1. Authentication ✅
- [ ] Login with <PERSON> (`20123456`)
- [ ] Verify user data loads correctly
- [ ] Check authentication state

### 2. Home Page ✅
- [ ] See 16 items with real images
- [ ] Scroll through items grid
- [ ] Tap an item to view details
- [ ] Check multiple images per item

### 3. Stores ✅
- [ ] Navigate to Store tab
- [ ] See 2 stores (<PERSON>'s Electronics, Fatima's Fashion)
- [ ] Tap a store to view details
- [ ] See store items (4 per store)

### 4. Discussions ✅
- [ ] Navigate to Inbox
- [ ] See 5-8 existing conversations
- [ ] Open a discussion
- [ ] Read conversation history with offers

### 5. Orders ✅
- [ ] Go to Profile → My Orders
- [ ] See 8-10 orders with various statuses
- [ ] Open order details
- [ ] Check payment screenshots

---

## 🔍 **15-Minute Detailed Test**

### User Switching Test:
1. **Ahmed (Store Owner)**:
   - [ ] Login and check store dashboard
   - [ ] View "Boutique Ahmed Electronics"
   - [ ] Check 4 items in store
   - [ ] View store orders

2. **Fatima (Store Owner)**:
   - [ ] Login and check store dashboard
   - [ ] View "Fashion Fatima"
   - [ ] Check 4 items in store
   - [ ] Test French language content

3. **Omar (Regular User)**:
   - [ ] Login and browse items
   - [ ] View discussions as buyer
   - [ ] Check orders made
   - [ ] Test English language

4. **Aicha (Regular User)**:
   - [ ] Login and browse items
   - [ ] Test Arabic language content
   - [ ] View user profile
   - [ ] Check liked items

---

## 🎯 **Feature-Specific Tests**

### Images & Optimization:
- [ ] All item images load (2-4 per item)
- [ ] Store images display (3 per store)
- [ ] Payment screenshots visible
- [ ] Images are WebP format
- [ ] Loading speed acceptable

### Multilingual Content:
- [ ] Arabic content displays correctly
- [ ] French content displays correctly
- [ ] English content displays correctly
- [ ] Language switching works

### Data Relationships:
- [ ] Items linked to correct users
- [ ] Store items show in store pages
- [ ] Orders reference correct items/users
- [ ] Discussions link buyers/sellers
- [ ] Payment proofs match orders

### Real-World Scenarios:
- [ ] Browse as guest (no login)
- [ ] Login and see personalized content
- [ ] Store owners see their items
- [ ] Regular users see all items
- [ ] Discussions show realistic conversations

---

## 🚨 **Critical Issues to Check**

### Must Work:
- [ ] **Login with all 4 users**
- [ ] **16 items display with images**
- [ ] **2 stores accessible**
- [ ] **Discussions readable**
- [ ] **Orders viewable**

### Performance:
- [ ] **App doesn't crash**
- [ ] **Images load within 3 seconds**
- [ ] **Smooth scrolling**
- [ ] **No memory leaks**

### Data Integrity:
- [ ] **No broken images**
- [ ] **All relationships intact**
- [ ] **Correct user permissions**
- [ ] **Realistic data displayed**

---

## 📊 **Expected Data Counts**

After seeding, you should see:
- **Users**: 4 (2 store owners, 2 regular)
- **Stores**: 2 (Ahmed's Electronics, Fatima's Fashion)
- **Items**: 16 (4 per user, with 2-4 images each)
- **Orders**: 8-10 (various statuses)
- **Discussions**: 5-8 (with realistic conversations)
- **Payment Proofs**: Multiple (with screenshots)
- **Locations**: 4 (Nouakchott areas)

---

## 🔧 **Troubleshooting**

### If Login Fails:
```bash
# Check if seeder ran correctly
php artisan tinker
>>> App\Models\User::count()  // Should return 4
>>> App\Models\User::pluck('phone')  // Should show phone numbers
```

### If Images Don't Load:
- Check internet connection
- Verify storage permissions
- Check Laravel logs: `tail -f storage/logs/laravel.log`

### If Data Missing:
```bash
# Re-run seeder
php artisan db:seed-rich --clean
```

---

## 📝 **Quick Test Report Template**

```
Date: ___________
Tester: ___________

✅ PASSED / ❌ FAILED

Authentication:
- Ahmed login: ___
- Fatima login: ___
- Omar login: ___
- Aicha login: ___

Core Features:
- Home page items: ___
- Store browsing: ___
- Discussions: ___
- Orders: ___
- Images loading: ___

Performance:
- App startup: ___
- Scrolling: ___
- Image loading: ___

Issues Found:
1. ________________
2. ________________
3. ________________

Overall Status: ✅ READY / ❌ NEEDS WORK
```

---

## 🎯 **Success Criteria**

Your app is ready for demo/production when:
- ✅ All 4 users can login
- ✅ All 16 items display with images
- ✅ Both stores are accessible
- ✅ Discussions are readable
- ✅ Orders show payment proofs
- ✅ No crashes during basic navigation
- ✅ Images load within reasonable time
- ✅ Multilingual content works

**Time to complete full test: 15-20 minutes**

---

Use this checklist to quickly verify your app works correctly with the rich seeded data! 🚀
