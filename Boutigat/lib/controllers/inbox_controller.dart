import 'dart:convert';
import 'dart:developer';

import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/models/messages.dart';
import 'package:boutigak/data/services/conversation_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class InboxController extends GetxController {
  var messagesSelected = true.obs;
  var notificationsSelected = false.obs;
  var discussions = <dynamic>[].obs;
  var isLoading = false.obs;
  var currentPage = 1.obs;
  var lastPage = 1.obs;
  var totalDiscussions = 0.obs;
  var hasMoreDiscussions = true.obs;
  final ScrollController scrollController = ScrollController();

  AuthController authController = Get.find<AuthController>();

  @override
  void onInit() {
    if (authController.isAuthenticated.value) {
      // Fetch discussions when accessing the page
      fetchDiscussions();
    }

    // Setup scroll controller for infinite scrolling
    scrollController.addListener(_scrollListener);

    super.onInit();
  }

  @override
  void onClose() {
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    super.onClose();
  }

  void _scrollListener() {
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200 &&
        !isLoading.value &&
        hasMoreDiscussions.value) {
      loadMoreDiscussions();
    }
  }

  void selectMessages() {
    messagesSelected.value = true;
    notificationsSelected.value = false;
  }

  void selectNotifications() {
    messagesSelected.value = false;
    notificationsSelected.value = true;
  }

  void openConversation(Map<String, dynamic> item, int discussionId, interlocutor, isStoreDiscussion) {
    final connectedUserId = Get.find<AuthController>().user?.id;
    final user = item['buyer_id'] == connectedUserId ? item['seller'] : item['buyer'];

    // If it's a store discussion, use store name as interlocutor
    final String displayInterlocutor = isStoreDiscussion && item['store'] != null
        ? item['store']['name']
        : interlocutor;

    Get.to(() => ConversationPage(
      itemId: item['item']['id'],
      item: item['item'],
      discussionId: discussionId,
      interlocutor: displayInterlocutor,
      isStoreDiscussion: isStoreDiscussion,
      user: user
    ));
  }

  Future<void> fetchDiscussions() async {

    print('fetchDiscussions called');
    isLoading.value = true;
    currentPage.value = 1; 

    try {
      Map<String, dynamic>? response = await ConversationService.getDiscussions(page: currentPage.value);


      log('res[onse fecth discuusion ${response}');

      
      if (response != null) {
        List<dynamic> fetchedDiscussions = response['data'] as List<dynamic>;

        log('fetched discussions api ${fetchedDiscussions}');
        discussions.value = fetchedDiscussions;


        log('fetched discussions ${discussions.value}');

        var pagination = response['pagination'];
        currentPage.value = pagination['current_page'];
        lastPage.value = pagination['last_page'];
        totalDiscussions.value = pagination['total'];
        hasMoreDiscussions.value = currentPage.value < lastPage.value;
      }
    } catch (e) {
      log("Error fetching discussions: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadMoreDiscussions() async {
    if (isLoading.value || !hasMoreDiscussions.value) return;

    isLoading.value = true;
    try {
      Map<String, dynamic>? response = await ConversationService.getDiscussions(
        page: currentPage.value + 1
      );

      if (response != null) {
        List<dynamic> newDiscussions = response['data'] as List<dynamic>;
        discussions.addAll(newDiscussions);

        // Update pagination info
        var pagination = response['pagination'];
        currentPage.value = pagination['current_page'];
        lastPage.value = pagination['last_page'];
        totalDiscussions.value = pagination['total'];
        hasMoreDiscussions.value = currentPage.value < lastPage.value;
      }
    } catch (e) {
      // Get.snackbar("Error", "Failed to load more discussions", subtitle: e.toString());
      log("Error loading more discussions: $e");
    } finally {
      isLoading.value = false;
    }
  }
}




class ConversationController extends GetxController {
  var messages = <Message>[].obs;
  var newMessage = ''.obs;
  TextEditingController textController = TextEditingController();
  var isMakingOffer = false.obs;
  var offerAmount = 0.0.obs;
  var isloading = false.obs;


  Future<dynamic> createDiscussion(int itemId) async {
    dynamic success = await ConversationService.createDiscussion(itemId);

    return success;
}


Future<dynamic> createStoreDiscussion(int itemId , int storeId) async {
  try {
    final response = await WebService.post(
      'api/discussions',
      body: {
        'item_id': itemId,
        'store_id': storeId,
        'is_store_discussion': true
      },
    );



    log('responbse store discussion ${response.body} ${response.statusCode}'  );



    if (response.statusCode == 200 || response.statusCode == 201) {
      return

        jsonDecode(response.body);
    }
    return null;
  } catch (e) {
    print('Error creating store discussion: $e');
    return null;
  }
}
  void sendMessage(int discussionId, bool isOffer, double? price, bool isStoreDiscussion) async {
    if (newMessage.value.isNotEmpty) {
      isloading.value = true;
      bool success = await ConversationService.sendMessage(discussionId, newMessage.value, isOffer, price, isStoreDiscussion);

      log('send message status ${success}');
      if (success) {
        // Clear input fields immediately
        newMessage.value = '';
        textController.clear();

        // Refresh discussion details from API
        await loadDiscussionDetails(discussionId);
      }
      isloading.value = false;
    }
  }

  void addOfferMessage(int discussionID , double offerAmount) async {
    if (offerAmount <= 0) {
     // // Get.snakbar("Invalid Offer", "Please enter a valid offer amount.");
      return;
    }
    bool success = await ConversationService.sendMessage(discussionID ,'', true , offerAmount , false);
    if (success) {

      // print("offer ");
      messages.add( Message(
        content: '$offerAmount',
        price: offerAmount,
        sentByMe: true,
        timestamp: DateTime.now(),
        isOffer: true,
      ));
    }
  }

  void receiveMessage(String content) {
    messages.add(Message(
      content: content,
      sentByMe: false,
      timestamp: DateTime.now(),
    ));
  }

Future<void> loadDiscussionDetails(int discussionId) async {

    try {
      List<Message>? discussionDetails = await ConversationService.getDiscussionDetails(discussionId);
      if (discussionDetails != null) {
        messages.assignAll(discussionDetails);
      } else {
      //  // Get.snakbar("Error", "Failed to load discussion details");
      }
    } catch (e) {
    //  // Get.snakbar("Error", "Failed to load discussion details: $e");
      print("Error loading discussion details: $e");
    } finally {

    }
  }

  String _getDateCategory(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateToCheck = DateTime(date.year, date.month, date.day);

    if (dateToCheck == today) {
      return 'Today';
    } else if (dateToCheck == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat('MMMM d, y').format(date);
    }
  }
}
