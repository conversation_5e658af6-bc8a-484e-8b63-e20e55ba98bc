import 'dart:developer';

import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/models/location.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/categories_service.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';



class StoreController extends GetxController with StateMixin<List<Category>> {


  // auth controller
  final AuthController authController = Get.find<AuthController>();

  // Store Lists
  RxList<Store> promotedStores = <Store>[].obs;
  RxList<Store> recommendedStores = <Store>[].obs;
  RxList<Store> FollowedStores = <Store>[].obs;
  var myStore = Rxn<Store>();
  RxList<Location> myStoreLocations = <Location>[].obs;
  RxBool isSaving = false.obs;
  // Categories
  RxList<Category> categories = <Category>[].obs;
  final _favoriteCategories = <Category>[].obs;
  List<Category> get FavoriteCategories => _favoriteCategories;
  Rx<Category?> selectedCategory = Rx<Category?>(null);

  // Store Details
  RxString name = ''.obs;
  RxString description = ''.obs;
  RxString type = ''.obs;
  RxString openingHours = "09:00".obs;
  RxString closingHours = "21:00".obs;
  RxList<String> boutiquePictures = <String>[].obs;

  // Store States
  RxBool isOnPromotion = false.obs;
  RxInt promotionPercent = 0.obs;
  RxBool isLoading = false.obs;
  RxBool isError = false.obs;
  RxBool isTypesLoading = false.obs;
  
Rx<LatLng> currentStorePosition = const LatLng(0.0, 0.0).obs;

  // Nom et adresse de la localisation (bindés au formulaire)
  RxString newStoreName = ''.obs;
  RxString newStoreAddress = ''.obs;

  // Add this to your StoreController class
  RxBool isStoreOpen = false.obs;

  // Items
  RxList<ItemController> allItems = RxList<ItemController>();
  RxList<Item> myItems = <Item>[].obs;

  // Store Types
  RxList<StoreType> storeTypes = <StoreType>[].obs;

  // Add currentStoreId to track the active store
  RxString currentStoreId = ''.obs;

  @override
  void onInit() {
    super.onInit();
    boutiquePictures.clear();
    // fetchStoreTypes();
    
    // Watch for store changes
    ever(currentStoreId, (_) {
      clearStoreData();
    });
  }

  // Clear store-specific data
  void clearStoreData() {
    myItems.clear();
    _favoriteCategories.clear();
    selectedCategory.value = null;
    update();
  }

  // Store Creation and Management
  Future<void> createStore() async {
    try {
      isSaving.value = true;

      if (!_validateStoreFields()) {
        return;
      }

      Store newStore = Store(
        name: name.value,
        description: description.value,
        typeId: int.parse(type.value),
        openingTime: openingHours.value,
        closingTime: closingHours.value,
        images: boutiquePictures,
      );

      debugPrint('Creating store: ${newStore.toJson()}');
      
      bool success = await StoreService.postStore(newStore);

      if (success) {

        await authController.getAndSaveUser();



       // // Get.snakbar("Success", "Store successfully created");
      } else {
      //  // Get.snakbar("Error", "Failed to create store");
      }
    } catch (e) {
    //  // Get.snakbar("Error", "Failed to create store: $e");
      debugPrint('Store creation error: $e');
    } finally {
      isSaving.value = false;
    }
  }

  bool _validateStoreFields() {
    if (name.value.isEmpty ||
        description.value.isEmpty ||
        type.value.isEmpty ||
        openingHours.value.isEmpty ||
        closingHours.value.isEmpty ||
        boutiquePictures.isEmpty) {
    //  // Get.snakbar("Error", "Please fill in all required fields.");
      isLoading.value = false;
      return false;
    }
    return true;
  }

  // Categories Management
  Future<void> fetchStoreFavoriteCategories(String storeID) async {
    if (currentStoreId.value != storeID) {
      currentStoreId.value = storeID;
    }
    
    change(null, status: RxStatus.loading());
    
    try {
      final categories = await StoreService.fetchFavoriteCategories(storeID);
      
      if (categories != null && categories.isNotEmpty) {
        _favoriteCategories.assignAll(categories);
        change(categories, status: RxStatus.success());
      } else {
        _favoriteCategories.clear();
        change([], status: RxStatus.empty());
      }
    } catch (e) {
      _favoriteCategories.clear();
      change(null, status: RxStatus.error('Failed to load categories'));
      debugPrint("Error fetching store categories: $e");
    }
  }

  void selectCategory(Category category, String storeID) {
    if (selectedCategory.value != category) {
      selectedCategory.value = category;
      if (currentStoreId.value == storeID) {
        fetchStoreItems(storeID);
      }
      update();
    }
  }

  void selectMyCategory(Category category) {
    if (selectedCategory.value != category) {
      selectedCategory.value = category;
      fetchMyStoreItems();
      update();
    }
  }

  void selectAllCategories() {
    if (selectedCategory.value != null) {
      selectedCategory.value = null;
      update();
    }
  }

  // Store Types
  Future<void> fetchStoreTypes() async {
    if (storeTypes.isNotEmpty) return; // Don't fetch if we already have types
    
    try {
      isTypesLoading.value = true;
      var fetchedTypes = await StoreService.fetchStoreTypes();
      if (fetchedTypes != null) {
        storeTypes.clear();
        storeTypes.addAll(fetchedTypes);
        isError.value = false;
      } else {
        isError.value = true;
     //   // Get.snakbar("Error", "Failed to load store types");
      }
    } catch (e) {
      isError.value = true;
   //   // Get.snakbar("Error", "Failed to load store types: $e");
    } finally {
      isTypesLoading.value = false;
    }
  }

  // Store Images
  void addBoutiquePicture(String pictureUrl) {
    if (!boutiquePictures.contains(pictureUrl)) {
      boutiquePictures.add(pictureUrl);
    }
  }

  void removeBoutiquePicture(String pictureUrl) {
    boutiquePictures.remove(pictureUrl);
  }

  // Store Operations
  bool isOpenNow() {
    try {
      final now = DateTime.now();
      final format = DateFormat.Hm();
      final opening = format.parse(openingHours.value);
      final closing = format.parse(closingHours.value);
      
      final todayOpening = DateTime(
        now.year, now.month, now.day, opening.hour, opening.minute
      );
      var todayClosing = DateTime(
        now.year, now.month, now.day, closing.hour, closing.minute
      );

      if (closing.hour < opening.hour) {
        todayClosing = todayClosing.add(const Duration(days: 1));
      }

      return now.isAfter(todayOpening) && now.isBefore(todayClosing);
    } catch (e) {
      debugPrint('Error checking store hours: $e');
      isError.value = true;
      return false;
    }
  }

  // Store Categories
  Future<void> fetchCategories() async {
    try {
      var fetchedCategories = await CategoryService.fetchCategories();
      if (fetchedCategories != null) {
        categories.value = fetchedCategories;
      }
    } catch (e) {
      debugPrint('Error fetching categories: $e');
    //  // Get.snakbar("Error", "Failed to fetch categories");
    }
  }

  Future<void> fetchMyFavoriteCategories() async {
    try {
      isLoading.value = true;
      var fetchedCategories = await StoreService.fetchMyFavoriteCategories();
      if (fetchedCategories != null) {
        _favoriteCategories.assignAll(fetchedCategories);
      }
      update();
    } catch (e) {
      debugPrint('Error fetching favorite categories: $e');
    //  // Get.snakbar("Error", "Failed to fetch favorite categories");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addFavoriteCategory(int categoryId) async {
    try {
      bool success = await StoreService.addFavoriteCategory(categoryId);
      if (success) {
        await fetchMyFavoriteCategories();

      //  // Get.snakbar("Success", "Category added to favorites");
      }
    } catch (e) {
      debugPrint('Error adding favorite category: $e');
   //   // Get.snakbar("Error", "Failed to add category to favorites");
    }
  }

  Future<void> deleteStoreFavoriteCategory(int categoryId) async {
    try {
      bool success = await StoreService.deleteStoreFavoriteCategory(categoryId);
      if (success) {
        _favoriteCategories.removeWhere((category) => category.id == categoryId);
      //  // Get.snakbar("Success", "Category removed from favorites");
      } else {
      //  // Get.snakbar("Error", "Failed to remove category");
      }
    } catch (e) {
      debugPrint('Error removing favorite category: $e');
   //   // Get.snakbar("Error", "Failed to remove category: $e");
    }
  }

  // Store Items
  Future<void> fetchStoreItems(String storeID) async {
    try {
      // Only clear items if we're fetching for a new store
      if (currentStoreId.value != storeID) {
        myItems.clear();
      }

      var fetchedItems = await StoreService.fetchStoreItems(storeID);
      if (fetchedItems != null) {
        myItems.value = fetchedItems;
      }
    } catch (e) {
      debugPrint('Error fetching store items: $e');
   //   // Get.snakbar("Error", "Failed to fetch store items");
    }
  }

  Future<void> fetchMyStoreItems() async {
    try {
      var fetchedItems = await StoreService.fetchMyStoreItems();
      if (fetchedItems != null) {
        myItems.value = fetchedItems;
      }
    } catch (e) {
      debugPrint('Error fetching my store items: $e');
  //    // Get.snakbar("Error", "Failed to fetch store items");
    }
  }

  // Store Lists
  Future<void> fetchPromotedStores() async {
    try {
      isLoading.value = true;
      var fetchedStores = await StoreService.fetchPromotedStores();
      if (fetchedStores != null) {
        promotedStores.clear();
        promotedStores.addAll(fetchedStores);
        isError.value = false;
      } else {
        isError.value = true;
    //    // Get.snakbar("Error", "Failed to load stores");
      }
    } catch (e) {
      isError.value = true;
    //  // Get.snakbar("Error", "Failed to load stores: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchRecomandedStores() async {
    try {
      log('fetchRecomandedStores called');
      isLoading.value = true;

      // Check if user is authenticated and call appropriate endpoint
      List<Store>? fetchedStores;
      if (authController.isAuthenticated.value) {
        // User is authenticated - fetch personalized recommended stores
        fetchedStores = await StoreService.fetchPersonalizedRecomandedStores();
        log('Fetching personalized recommended stores for authenticated user');
      } else {
        // User is not authenticated - fetch general recommended stores
        fetchedStores = await StoreService.fetchRecomandedStores();
        log('Fetching general recommended stores for guest user');
      }

      if (fetchedStores != null) {
        recommendedStores.clear();
        recommendedStores.addAll(fetchedStores);
        log('fetchedStores ${fetchedStores.length} stores');
        isError.value = false;
      } else {
        isError.value = true;
     //   // Get.snakbar("Error", "Failed to load recommended stores");
      }
    } catch (e) {
      isError.value = true;
      log('Error fetching recommended stores: $e');
    //  // Get.snakbar("Error", "Failed to load recommended stores: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchFollowedStores() async {
    try {
     
      var fetchedStores = await StoreService.fetchFollowedStores();
      if (fetchedStores != null) {
        FollowedStores.clear();
        FollowedStores.addAll(fetchedStores);
        
      } else {
        
      //  // Get.snakbar("Error", "Failed to load followed stores");
      }
    } catch (e) {
     
    //  // Get.snakbar("Error", "Failed to load followed stores: $e");
    } 
  }

  Future<void> fetchedMyStoreInformation() async {
    try {

      log('my store information ......');

      var fetchedInfo = await StoreService.getMyStoreInformation();
      if (fetchedInfo != null) {
        myStore.value = fetchedInfo;
        // Add this line to get the store's open status
        isStoreOpen.value = myStore.value?.isOpen ?? false;
      }
    } catch (e) {
      debugPrint('Error fetching store information: $e');
   //   // Get.snakbar("Error", "Failed to fetch store information");
    }
  }


Future<void> setPromotionForItem(int itemId, double promotionPercentage) async {
  try {
    bool success = await StoreService.setPromotionForItem(itemId, promotionPercentage);
    if (success) {
      await fetchMyStoreItems(); // Rafraîchir la liste des articles après modification
    //  // Get.snakbar("Success", "Promotion set successfully");
    }
  } catch (e) {
    debugPrint('Error setting promotion: $e');
   // // Get.snakbar("Error", "Failed to set promotion");
  }
}
Future<void> removePromotionForItem(int itemId) async {
  try {
    bool success = await StoreService.removePromotionForItem(itemId);
    if (success) {
      await fetchMyStoreItems(); // Rafraîchir la liste des articles après suppression de la promo
    //  // Get.snakbar("Success", "Promotion removed successfully");
    }
  } catch (e) {
    debugPrint('Error removing promotion: $e');
  //  // Get.snakbar("Error", "Failed to remove promotion");
  }
}



  Future<void> updateStore() async {
    isLoading.value = true;
    
    try {



      log('boutiquePictures ${boutiquePictures}');
      final store = Store(
        name: name.value,
        description: description.value,
        typeId: int.parse(type.value),
        openingTime: openingHours.value,
        closingTime: closingHours.value,
        images: boutiquePictures,
      );
      
      final success = await StoreService.updateStore(store);
      
      if (success) {
        await fetchedMyStoreInformation();
      } else {
        isError.value = true;
        Get.snackbar(
          "Error",
          "Failed to update store",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      isError.value = true;
      Get.snackbar(
        "Error",
        "An error occurred: $e",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  // Met à jour la position sélectionnée
  void updateStorePosition(LatLng position) {
    currentStorePosition.value = position;
  }
 

  Future<void> fetchMyStoreLocations() async {
  try {
    Location? location = await StoreService.fetchMyStoreLocation();
    if (location != null) {
      myStoreLocations.value = [location];
      updateStorePosition(LatLng(location.latitude!, location.longitude!));
    }
  } catch (e) {
    print('Error fetching store location: $e');
  }
}


  Future<void> saveMyStoreLocation({
    required String name,
    required String address,
    required double latitude,
    required double longitude,
  }) async {
    isSaving.value = true;

    try {
      Map<String, dynamic> locationData = {
        'name': name,
        'address': address,
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
      };

      var response = await StoreService.storeMyStoreLocation(locationData);

      if (response != null) {
        // Convert and add to list
        Location newLocation = Location(
          id: response['location_id'],
          name: name,
          address: address,
          latitude: latitude,
          longitude: longitude,
        );

        myStoreLocations.add(newLocation);

        Get.snackbar(
          'Succès',
          'La localisation de votre boutique a été enregistrée',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Erreur',
          'Impossible d\'enregistrer la localisation',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Erreur',
        'Une erreur est survenue',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      print('Error saving store location: $e');
    } finally {
      isSaving.value = false;
    }
  }

  // Method to toggle store status
  Future<void> toggleStoreStatus(bool isOpen) async {
    try {

      log('is open ... ${isOpen}');

      isLoading(true);
      final response = await WebService.post(
        'api/stores/toggle-status',
        body: {'is_open': isOpen},
      );
      

      log('response .... ${response.statusCode}');
      log('response .... ${response.body}');

      if (response.statusCode == 200) {
        isStoreOpen.value = isOpen;
        Get.snackbar(
          "Success",
          isOpen ? "Store is now open" : "Store is now closed",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: isOpen ? Colors.green : Colors.orange,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          "Error",
          "Failed to update store status",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        "Error",
        "An error occurred: $e",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }

  void clearAllData() {
    promotedStores.clear();
    recommendedStores.clear();
    FollowedStores.clear();
    myStore.value = null;

    categories.clear();
    _favoriteCategories.clear();
    selectedCategory.value = null;

    name.value = '';
    description.value = '';
    type.value = '';
    openingHours.value = "09:00";
    closingHours.value = "21:00";
    boutiquePictures.clear();

    isOnPromotion.value = false;
    promotionPercent.value = 0;
    isLoading.value = false;
    isError.value = false;

    // Clear Items
    allItems.clear();
    myItems.clear();

    // Clear Store Types
    storeTypes.clear();

    // Reset current store ID
    currentStoreId.value = '';

    change(null, status: RxStatus.empty());

    update();
  }

  @override
  void onClose() {
    clearAllData();
    super.onClose();
  }
}
