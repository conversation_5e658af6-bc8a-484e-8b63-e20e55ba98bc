import 'dart:developer';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/deepLinkHandler.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/profil/profil_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/connectivity_widget.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:lottie/lottie.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';
import 'home_widgets.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'favorite_page.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:boutigak/controllers/ad_controller.dart';
import 'package:boutigak/data/models/ad.dart';

class HomePage extends StatefulWidget {
  final bool openDrawer;
  const HomePage({Key? key, this.openDrawer = false}) : super(key: key);

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final ItemController itemController = Get.find<ItemController>();
  final AuthController authController = Get.put(AuthController());
  final StoreController storeController = Get.put(StoreController());
  final AdController adController = Get.put(AdController());
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  
  // Add controller for carousel

  

    final CarouselSliderController carouselController = CarouselSliderController();

  final RxBool isLoading = false.obs;

  Future<void> _refreshData() async {
    isLoading.value = true;
    await itemController.fetchItems(refresh: true);
    await storeController.fetchRecomandedStores();
    await storeController.fetchPromotedStores(); // Add this line for promoted stores
    await adController.fetchAds(); // Add this line
    await Future.delayed(Duration(milliseconds: 1600));
    _refreshController.refreshCompleted();
    isLoading.value = false;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshData(); 
      if (widget.openDrawer) {

        print('Opening drawer from HomePage');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ZoomDrawer.of(context)?.open();
        });
      }
    });
  }

  @override
  void dispose() {
    _refreshController.dispose(); // ✅ ici
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ConnectivityPopup.show(context);
    });

    ScreenUtil.init(context);
    double screenWidth = MediaQuery.of(context).size.width;

    return Center(
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: AppBar(
          elevation: 0,
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.symmetric(
                  horizontal: MediaQuery.of(context).size.width * 0.05,
                ),
                child: ColorFiltered(
                  colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
                  child: Image.asset(
                    'assets/images/biglogo_boutigak.png',
                    width: 100,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (!authController.isAuthenticated.value) {
                    Get.to(() => LoginPage());
                  } else {
                    ZoomDrawer.of(context)!.toggle();
                  }
                },
                child: Container(
                  margin: EdgeInsets.only(
                    right: MediaQuery.of(context).size.width * 0.04,
                    left: MediaQuery.of(context).size.width * 0.04,
                  ),
                  child: authController.isAuthenticated.value
                      ? CircleAvatar(
                          radius: 22,
                          backgroundColor: Colors.grey[300],
                          child: Text(
                            '${authController.user?.firstName[0] ?? ''}${authController.user?.lastName[0] ?? ''}'.toUpperCase(),
                            style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      : Container(
                          padding: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            shape: BoxShape.rectangle,
                            border: Border.all(
                              color: Theme.of(context).colorScheme.onSurface,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            FontAwesomeIcons.solidUser,
                            size: 15,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
        body: SmartRefresher(
          controller: _refreshController,
          enablePullDown: true,
          header:  CustomHeader(
        height: 100.h,
        builder: (context, mode) {
      if (mode == RefreshStatus.refreshing) {
        return Lottie.asset(
          'assets/lottie/loader.json',
          width: 75.w,
          height: 75.w,
        );
      } else if (mode == RefreshStatus.completed) {
        return Lottie.asset(
          'assets/lottie/Done.json',
          width: 65.w,
          height: 65.w,
          repeat: false,
        );
      } else if (mode == RefreshStatus.failed) {
        return Icon(Icons.error, color: Colors.red);
      } else {
        return Icon(Icons.arrow_downward); // idle / canRefresh
      }
        },
      ),
      
          onRefresh: _refreshData,
          child: CustomScrollView(
            controller: itemController.scrollController,
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h, top: 6.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomButtonToday(
                        text: 'following'.tr,
                        onPressed: () {
                          if (authController.isAuthenticated.value) {
                            Get.to(FollowedStoresPage());
                          } else {
                            Get.to(() => LoginPage());
                          }
                        }
                      ),
                      CustomButtonToday(
                        text: 'favorites'.tr,
                        onPressed: () {
                          if (authController.isAuthenticated.value) {
                            Get.to(FavoritePage());
                          } else {
                            Get.to(() => LoginPage());
                          }
                        }
                      ),
                    ],
                  ),
                ),
              ),
              
              // Add the ad carousel here
              SliverToBoxAdapter(
                child: _buildAdCarousel(),
              ),
              
              SliverToBoxAdapter(
                child: SizedBox(height: 8.h),
              ),
              
              SliverToBoxAdapter(
                child: Row(
                  children: [
                    SizedBox(width: 16.w),
                    Text(
                      "shop_we_think_you_love".tr,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: AppTextSizes.subHeading,
                        fontWeight: AppFontWeights.bold
                      ),
                    ),
                  ],
                ),
              ),
              SliverToBoxAdapter(
                child: SizedBox(height: 8.h),
              ),
              SliverToBoxAdapter(
                child: _buildBoutiqueScrollWidget(),
              ),
              SliverToBoxAdapter(
                child: SizedBox(height: 8.h),
              ),
              SliverToBoxAdapter(
                child: Row(
                  children: [
                    SizedBox(width: 16.w),
                    Text(
                      "you_might_like".tr,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: AppTextSizes.subHeading,
                        fontWeight: AppFontWeights.bold
                      ),
                    ),
                  ],
                ),
              ),
            Obx(() {
        if (itemController.isLoading.value || itemController.items.isEmpty) {
      return SliverToBoxAdapter(
        child: buildShimmerGrid(context),
      );
        }
      
        return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      sliver: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: MediaQuery.of(context).size.width < 360 ? 0.65 : 344 / 537,
          crossAxisSpacing: MediaQuery.of(context).size.width * 0.03,
          mainAxisSpacing: 10.0,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index >= itemController.items.length) return null;
            return LayoutBuilder(
              builder: (context, constraints) {
                return ItemWidget(item: itemController.items[index]);
              },
            );
          },
          childCount: itemController.items.length,
        ),
      ),
        );
      }),
      
      
            ],
          ),
        ),
      ),
    );
  }

Widget buildBoutiqueShimmerEffect(BuildContext context) {
  double squareSize = MediaQuery.of(context).size.width * 0.20;

  return Padding(
    padding: const EdgeInsets.only(right: 16.0),
    child: SizedBox(
      height: squareSize,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 5, // Nombre de boutiques fictives en Shimmer
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.only(left: 16.0),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: squareSize,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15.0),
                  color: Colors.grey[300],
                ),
              ),
            ),
          );
        },
      ),
    ),
  );
}




  //  Widget intégré des boutiques recommandées
 Widget _buildBoutiqueScrollWidget() {
  double squareSize = MediaQuery.of(context).size.width * 0.20;

  return Padding(
    padding: const EdgeInsets.only(right: 16.0),
    child: SizedBox(
      height: squareSize,
      child: Obx(() {
        if (storeController.isLoading.value || storeController.promotedStores.isEmpty) {
          return buildBoutiqueShimmerEffect(context);
        }

        return ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: storeController.promotedStores.length,
          itemBuilder: (context, index) {
            var boutique = storeController.promotedStores[index];

            return GestureDetector(
              onTap: () {
                // Create or get OrderController without tag
                final OrderController orderController = Get.put(OrderController(initialOrderId: 1));
                // Clear any existing items
                orderController.clearOrder();
                print('store images first: ${boutique.images.first}');
                Get.to(() => StoreDetailsPage(store: boutique));
              },
              child: Container(
                width: squareSize,
                margin: const EdgeInsets.only(left: 16.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey, width: 0.5), // ✅ Bordure grise de 1px
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: FadeInImage(
                    placeholder: const AssetImage('assets/images/placeholder_logo.png'),
                    image: boutique.images.isNotEmpty
                        ? NetworkImage('${boutique.images.first}')
                        : const AssetImage('assets/images/placeholder_logo.png') as ImageProvider,
                    fit: BoxFit.cover,
                    placeholderFit: BoxFit.cover,
                    fadeInDuration: const Duration(milliseconds: 300),
                    imageErrorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey,
                        alignment: Alignment.center,
                        child: Image.asset('assets/images/placeholder_logo.png'),
                      );
                    },
                  ),
                ),
              ),
            );
          },
        );
      }),
    ),
  );
}

Widget buildShimmerGrid(BuildContext context) {
  double screenWidth = MediaQuery.of(context).size.width;
  double sidePadding = screenWidth * 0.0407;

  return GridView.builder(
    shrinkWrap: true,
    physics: const NeverScrollableScrollPhysics(),
    padding: EdgeInsets.all(sidePadding),
    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 2, 
      childAspectRatio: 344 / 550, 
      crossAxisSpacing: sidePadding,
      mainAxisSpacing: 10,
    ),
    itemCount: 6, // ✅ Nombre de faux items en Shimmer
    itemBuilder: (context, index) {
      return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: screenWidth * 0.4388,
              height: screenWidth * 0.5485,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[300],
              ),
            ),
            SizedBox(height: sidePadding),
            Container(
              width: screenWidth * 0.3,
              height: 14.0,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 8.0),
            Align(
              alignment: Alignment.centerRight,
              child: Container(
                width: screenWidth * 0.2,
                height: 12.0,
                color: Colors.grey[300],
              ),
            ),
          ],
        ),
      );
    },
  );
}

  Widget _buildAdCarousel() {
  return Obx(() {
    if (adController.isLoading.value || adController.ads.isEmpty) {
      return _buildAdCarouselShimmer();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CarouselSlider.builder(
          carouselController: carouselController,
          itemCount: adController.ads.length,
          options: CarouselOptions(
            height: 150.h,
            viewportFraction: 0.92,
            padEnds: false,
            enlargeCenterPage: false,
            enableInfiniteScroll: true,
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 5),
            autoPlayAnimationDuration: const Duration(milliseconds: 800),
            autoPlayCurve: Curves.easeInOut,
            onPageChanged: (index, reason) {
              adController.currentAdIndex.value = index;
            },
          ),
          itemBuilder: (context, index, realIndex) {
            final ad = adController.ads[index];
            return GestureDetector(
              onTap: () => _handleAdTap(ad),
              child: Container(
                margin: EdgeInsets.only(left: index == 0 ? 16 : 8, right: 2),
                child: CachedImageWidget(
                  imageUrl: '${ad.imageUrl}' ,
                  width: double.infinity,
                  height: 150.h,
                  borderRadius: BorderRadius.circular(12),
                  fit: BoxFit.cover,
                ),
              ),
            );
          },
        ),
        SizedBox(height: 10.h),
        Center(
          child: Obx(() => Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(adController.ads.length, (index) {
              final isActive = index == adController.currentAdIndex.value;
              return AnimatedContainer(
                duration: Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                height: 6,
                width: isActive ? 18 : 8,
                decoration: BoxDecoration(
                  color: isActive
                      ? AppColors.primary
                      : AppColors.primary.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
              );
            }),
          )),
        ),
      ],
    );
  });
} 
  
  // Handle ad tap based on type
  void _handleAdTap(Ad ad)  async{

    print('${ad.imageUrl}');
    switch (ad.targetType) {
      case 'item':
        
        await itemController.fetchItemById(ad.targetId as int);
      
      if (itemController.selectedItem.value != null) {


        Get.to(() => ItemDetailsPage(item: itemController.selectedItem.value!));
      }
        break;
      case 'store':
       
       print('in store ... ${ad.targetId}');
       Store? store = await StoreService.getStoreById(ad.targetId as int);

        print('get store id ${store}');

      // Navigate to store with the selected item ID
        Get.to(() => StoreDetailsPage(
          store: store!,
        ));
        break;
      case 'item-store':
      



        log('item store ${ad.storeId}');
        Store? store = await StoreService.getStoreById(ad.storeId as int);
    

      // Navigate to store with the selected item ID
        Get.to(() => StoreDetailsPage(
          store: store!,
        selectedItemId: ad.targetId,

        ));
        break;
    }
  }
  
  // Shimmer effect for ad carousel
 Widget _buildAdCarouselShimmer() {
 
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          height: 150.h,
          margin: const EdgeInsets.only(left: 16, right: 36), // ✅ même marge que l'image
          width: MediaQuery.of(context).size.width * 0.92,   // ✅ même largeur que le carrousel
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.grey[300],
          ),
        ),
      ),
      SizedBox(height: 10.h),
      Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(4, (index) {
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              height: 6,
              width: index == 0 ? 18 : 8,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4),
              ),
            );
          }),
        ),
      ),
    ],
  );
}
}
