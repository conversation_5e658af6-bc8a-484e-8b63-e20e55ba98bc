import 'dart:ui';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:lottie/lottie.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '/constants/app_colors.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// ✅ FavoritePage with ScreenUtil integrated
class FavoritePage extends StatefulWidget {
  @override
  _FavoritePageState createState() => _FavoritePageState();
}

class _FavoritePageState extends State<FavoritePage> {
  final ItemController itemController = Get.find<ItemController>();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLikedItems();
    });
  }

  Future<void> _loadLikedItems() async {
    setState(() => _isLoading = true);
    try {
     await itemController.fetchLikedItems();
    } catch (e) {
      print("Erreur fetchLikedItems: $e");
    }
    setState(() => _isLoading = false);
  }

  Future<void> _onRefresh() async {
    try {
    await itemController.fetchLikedItems();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: Size(375, 812));

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'favorites'.tr,
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: AppTextSizes.subHeading.sp,
          ),
        ),
      ),
      body: SmartRefresher(
        controller: _refreshController,
        enablePullDown: true,
        enablePullUp: false,
        onRefresh: _onRefresh,
        header: CustomHeader(
          height: 100.h,
          builder: (context, mode) {
            return Lottie.asset(
              'assets/lottie/loader.json',
              width: 75.w,
              height: 75.h,
              repeat: true,
              animate: true,
            );
          },
        ),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: _isLoading
                ? _buildShimmerList()
                : Obx(() {
                    final likedItems = itemController.likedItems;
                    if (likedItems.isEmpty) {
                      return SizedBox(
                        height: MediaQuery.of(context).size.height * 0.6,
                        child: Center(child: Text("No favorite items")),
                      );
                    }
                    return ListView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: likedItems.length,
                      itemBuilder: (context, index) {
                        final item = likedItems[index];
                        return LikedItemWidget(
                          key: ValueKey(item.id),
                          item: item,
                          isFavorited: item.isLiked,
                          toggleFavorite: () {},
                        );
                      },
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.0.r),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 4,
                offset: Offset(0.4, 0.4),
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 60.w,
                  height: 70.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 90.w,
                            height: 14.h,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                          ),
                        ),
                        SizedBox(height: 6.h),
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 60.w,
                            height: 12.h,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(5.r),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 60.w,
                            height: 10.h,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                          ),
                        ),
                        SizedBox(height: 6.h),
                        Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 50.w,
                            height: 14.h,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(width: 90.w),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 28.w,
                  height: 28.w,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class LikedItemWidget extends StatefulWidget {
  final Item item;
  final bool isFavorited;
  final void Function()? toggleFavorite;

  LikedItemWidget({
    Key? key,
    required this.item,
    required this.isFavorited,
    required this.toggleFavorite,
  }) : super(key: key);

  @override
  _LikedItemWidgetState createState() => _LikedItemWidgetState();
}

class _LikedItemWidgetState extends State<LikedItemWidget> {
  late bool isFavorited;

  @override
  void initState() {
    super.initState();
    isFavorited = widget.isFavorited;
  }
Future<void> _toggleFavorite() async {
  bool success = await ItemService.likeUnlikeItem(widget.item.id!, isFavorited);
  if (success) {
    setState(() {
      isFavorited = !isFavorited;
    });

    // Supprime de la liste des favoris si c'est un "dislike"
    if (!isFavorited) {
      final ItemController itemController = Get.find<ItemController>();
      itemController.likedItems.removeWhere((i) => i.id == widget.item.id);
    }
  }
}

void _showModalBottomSheet(BuildContext context, Item item) async {
  // If item belongs to a store, navigate to store details with the item selected
  if (item.storeId != null) {
    try {
      Store? store = await StoreService.getStoreById(item.storeId!);
      
      if (store != null) {
        Get.to(() => StoreDetailsPage(
          store: store,
          selectedItemId: item.id,
        ));
        return; // Exit early since we're navigating to store
      }
    } catch (e) {
      debugPrint('Error fetching store: $e');
    }
  }

  // Continue with normal bottom sheet for non-store items
  PageController pageController = PageController();
  int currentPage = 0;
  bool isFavorited = false;

  // Get the controllers
  ConversationController conversationController = Get.put(ConversationController());
  ItemController itemController = Get.find<ItemController>();

  // Set the item in the controller so it can be used reactively
  itemController.selectItem(item);
  print("item id ====${item.id}====");
  showDialog(
  barrierDismissible: false,
  context: context,
  builder: (context) => Container(
    color: Colors.black.withOpacity(0.4),
    child: Center(
      child: CupertinoTheme(
        data: const CupertinoThemeData(
          brightness: Brightness.dark, // force le spinner à être blanc
        ),
        child: const CupertinoActivityIndicator(radius: 15),
      ),
    ),
  ),
);

    
    itemController.fetchItemById(item.id!).then((_) {
      Navigator.pop(context); // Ferme le loader
  // Fetch the latest item data before showing the bottom sheet
  
    showModalBottomSheet(
      backgroundColor: Theme.of(context).colorScheme.surface,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Obx(() {
              final updatedItem = itemController.selectedItem.value;

              if (updatedItem == null) {
                return Center(child: CircularProgressIndicator());
              }

              return Container(
                height: MediaQuery.of(context).size.height,
                child: Stack(
                  children: [
                    Column(
                      children: [
                        Expanded(
                          child: Stack(
                            children: [
                              ImageSlider(
                                pageController: pageController,
                                photos: updatedItem.images.map((image) => '$image').toList(),
                                currentPage: currentPage,
                                onPageChanged: (int page) => setState(() => currentPage = page),
                                borderRadius: 0,
                              ),
                              Positioned(
                                top: 60,
                                right: 10,
                                child: ClipOval(
                                  child: BackdropFilter(
                                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                                    child: Container(
                                      color: AppColors.onBackground.withOpacity(0.2),
                                      child: IconButton(
                                        iconSize: 20,
                                        icon: Icon(FontAwesomeIcons.upRightFromSquare, color: AppColors.background),
                                        onPressed: () => print('Share Icon Tapped!'),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 60,
                                left: 10,
                                child: ClipOval(
                                  child: BackdropFilter(
                                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                                    child: Container(
                                      color: AppColors.onBackground.withOpacity(0.3),
                                      child: IconButton(
                                        iconSize: 20,
                                        icon: Icon(FontAwesomeIcons.chevronDown, color: AppColors.background),
                                        onPressed: () => Navigator.pop(context),
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          child: Container(),
                        ),
                      ],
                    ),
                    Positioned(
                      top: MediaQuery.of(context).size.height * 0.49,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
                        ),
                        child: Column(
                          children: [
                            InfoSection(
                              item: updatedItem, // Use the updated item data
                              isFavorited: isFavorited,
                              toggleFavorite: () => setState(() => isFavorited = !isFavorited),
                            ),
                           
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
                        ),
                        child:
                            Padding(
                              padding: const EdgeInsets.only(bottom: 30.0,left: 25,right: 25),
                              child: Row(
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Price (mru)",
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: Theme.of(context).disabledColor,
                                        ),
                                      ),
                                      Text(
                                        "${updatedItem.price.toStringAsFixed(0)} ",
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Theme.of(context).colorScheme.onSurface,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Spacer(),
                                  Container(
                                    width: 200,  // Button width
                                    height: 50,  // Button height
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.onSurface,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          spreadRadius: 2,
                                          blurRadius: 5,
                                          offset: Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: TextButton(
                                      onPressed: () async {
                                        dynamic success = await conversationController.createDiscussion(item.id!);

                                        if (success != null) {
                                          Get.to(() => ConversationPage(
                                            itemId: item.id!,
                                            item: updatedItem.toJson(),
                                            discussionId: success['discussion']['id'],
                                            interlocutor: item.userName!,
                                          ));
                                        }
                                      },
                                      style: TextButton.styleFrom(
                                        padding: EdgeInsets.zero,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(30),
                                        ),
                                      ),
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: Text(
                                          "Make an offer",
                                          style: TextStyle(
                                            color: AppColors.primary,
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          
                        
                      ),
                    ),
                  ],
                ),
              );
            });
          },
        );
      },
    );
  });
}

  @override
 Widget build(BuildContext context) {
  return GestureDetector(
    onTap: () {
      _showModalBottomSheet(context, widget.item);
    },
    child: Container(
      margin:EdgeInsets.symmetric(horizontal: 8.0, vertical: 4), 
      padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,// Fond blanc
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0.4, 0.4), // Position de l'ombre
          ),
        ],
      ),
      child: Row(
        children: [
           CachedImageWidget(
      imageUrl: '${widget.item.images.first}',
      width: 60,
      height: 70,
      fit: BoxFit.cover,
  borderRadius: BorderRadius.circular(8.0),
  ),
          SizedBox(width: 8.0), // Espace réservé à l'image (pour éviter un chevauchement)
          Expanded(
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 145,
                      child: Text(
                        Get.locale?.languageCode == 'ar' ? widget.item.titleAr ?? widget.item.title : widget.item.title,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    
                    
                              Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Text(
                         widget.item.brandName ?? "Unknown",
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                            
                          ),
                          maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        ),
                      ),               
                  
                  ],
                ),
                
                Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "price".tr+"("+"mru".tr+")",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Theme.of(context).disabledColor,
    
                                    ),
                                  ),
                              
                                  Text(
                                    "${widget.item.price.toStringAsFixed(0)} ",
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).colorScheme.onSurface,
    
                                    ),
                                    maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                              Spacer(),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              isFavorited ? Icons.favorite : Icons.favorite_border,
              size: 28,
              color: Colors.red,
            ),
            padding: EdgeInsets.zero,
            onPressed: _toggleFavorite,
          ),
        ],
      ),
    ),
  );
}

}
