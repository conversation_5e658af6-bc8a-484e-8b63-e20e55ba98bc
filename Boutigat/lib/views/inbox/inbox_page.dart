import 'dart:developer';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/notifications_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:get/get.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:boutigak/data/services/conversation_service.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/data/models/messages.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:boutigak/controllers/badge_controller.dart';










class InboxPage extends StatefulWidget {
  @override
  State<InboxPage> createState() => _InboxPageState();
}

class _InboxPageState extends State<InboxPage> {
  final InboxController inboxController = Get.put(InboxController());
  final AuthController authController = Get.put(AuthController());
  final BadgeController badgeController = Get.find<BadgeController>();


  @override
  void initState() {
    super.initState();
    // Reset badges when viewing the inbox
    // if (inboxController.notificationsSelected.value) {
    //   badgeController.resetBadge('notifications');
    // } else {
    //   badgeController.resetBadge('messages');
    // }
  }

  // Function to refresh discussions
  Future<void> _refreshDiscussions() async {
    await inboxController.fetchDiscussions();
    await badgeController.fetchBadgeCounts();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    return Scaffold(
      appBar: CustomAppBar(
        titleText: "inbox".tr,
        icon: FontAwesomeIcons.solidUser,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
Row(
  children: [
    Expanded(
      child: Obx(() => CustomTabButton(
            text: 'messages'.tr,
            isSelected: inboxController.messagesSelected.value,
            onPressed: () {
              inboxController.selectMessages();
              badgeController.resetBadge('messages');
            },
            badgeCount: badgeController.getModuleCount('messages'),
          )),
    ),
    Expanded(
      child: Obx(() => CustomTabButton(
            text: 'notifications'.tr,
            isSelected: inboxController.notificationsSelected.value,
            onPressed: () {
              inboxController.selectNotifications();
              badgeController.resetBadge('notifications');
            },
            badgeCount: badgeController.getModuleCount('notifications'),
          )),
    ),
  ],
),

          Expanded(
            child: Obx(
              () => inboxController.isLoading.value
                  ? const Center(
      child: CupertinoTheme(
        data: CupertinoThemeData(
          brightness: Brightness.light,
        ),
        child: CupertinoActivityIndicator(radius: 18),
      ),
    )
                  : RefreshIndicator(
                      onRefresh: _refreshDiscussions,
                      child: inboxController.messagesSelected.value
                          ? MessageList()
                          : NotificationList(),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}


class MessageList extends StatefulWidget {
  @override
  State<MessageList> createState() => _MessageListState();
}

class _MessageListState extends State<MessageList> {
  final InboxController inboxController = Get.find();

  @override
  void initState() {
    super.initState();
  }

  // Function to make a phone call
  void _makePhoneCall(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      Get.snackbar(
        'Error',
        'Could not launch phone call',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Sort discussions by latest message timestamp
      final sortedDiscussions = List.from(inboxController.discussions)
        ..sort((a, b) {
          final aTime = a['latest_message'] != null
              ? DateTime.parse(a['latest_message']['created_at'])
              : DateTime.parse(a['created_at']);
          final bTime = b['latest_message'] != null
              ? DateTime.parse(b['latest_message']['created_at'])
              : DateTime.parse(b['created_at']);
          return bTime.compareTo(aTime); // Descending order (newest first)
        });

      return ListView.builder(
        controller: inboxController.scrollController,
        itemCount: sortedDiscussions.length + (inboxController.hasMoreDiscussions.value ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at the bottom when loading more items
          if (index == sortedDiscussions.length) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: CupertinoTheme(
                  data: CupertinoThemeData(
                    brightness: Brightness.light,
                  ),
                  child: CupertinoActivityIndicator(radius: 15),
                ),
              ),
            );
          }

          final item = sortedDiscussions[index];
          final connectedUserId = Get.find<AuthController>().user?.id;


          final buyerID  = item['buyer']['id'] ?? null;

          final sellerID  = item['seller']['id'] ?? null;

          log('buyer id $buyerID');
          log('seller id $sellerID');
          final user = buyerID == connectedUserId ? item['seller'] : item['buyer'];
          final bool isStoreDiscussion = item['is_store_discussion'] ?? false;

          return ListTile(
            leading: CircleAvatar(
              radius: 24,
              backgroundImage: NetworkImage(
                '${item['item']['images'][0]['url']}',
              ),
            ),
            title: Row(
              children: [
                if (isStoreDiscussion && item['store'] != null)
                  Text('${item['store']['name'] ?? 'Store'}')
                else if (user != null)
                  Text('${user['firstname'] ?? ''} ${user['lastname'] ?? ''}')
                else
                  Container(),
                Spacer(),
                if (user != null && user['phone'] != null)

                Container(),

              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,

              children: [
                Text(
                  item['latest_message']?['is_an_offer'] == true
                    ? (item['latest_message']['sent_by_me'] == true
                        ? 'You made an offer'
                        : 'You received an offer')
                    : '${item['latest_message']?['content'] ?? ''}',
                  style: TextStyle(color: Colors.grey),
                  maxLines: 1, // Limit to one line
                  overflow: TextOverflow.ellipsis, // Add ellipsis if text overflows
                ),
              ],
            ),
            trailing: Text(DateFormat('HH:mm').format(DateTime.parse(item['created_at'])), style: TextStyle(color: Colors.grey),),
            // latest message

            onTap: () => inboxController.openConversation(item, item['id'],user['firstname'] +" "+user['lastname'] , item['is_store_discussion']),
          );
        },
      );
    });
  }
}

class NotificationList extends StatefulWidget {
  @override
  State<NotificationList> createState() => _NotificationListState();
}

class _NotificationListState extends State<NotificationList> {
  final NotificationController notificationController = Get.put(NotificationController());
  final BadgeController badgeController = Get.find<BadgeController>();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load the initial set of notifications
    notificationController.fetchNotifications();

    // Reset the notification badge when viewing notifications
    badgeController.resetBadge('notifications');

    // Add scroll listener for pagination
    scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200 &&
        !notificationController.isLoading.value &&
        notificationController.currentPage.value * 15 < notificationController.totalNotifications.value) {
      notificationController.loadMoreNotifications();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (notificationController.isLoading.isTrue && notificationController.notifications.isEmpty) {
        // Show loading indicator while fetching the first page
        return const Center(
          child: CupertinoTheme(
            data: CupertinoThemeData(
              brightness: Brightness.light,
            ),
            child: CupertinoActivityIndicator(radius: 15),
          ),
        );
      } else if (notificationController.isError.isTrue && notificationController.notifications.isEmpty) {
        // Display an error message if there was an error loading notifications
        return Center(child: Text('Erreur lors du chargement des notifications'));
      } else {
        return ListView.builder(
          controller: scrollController,
          itemCount: notificationController.notifications.length + 1,
          itemBuilder: (context, index) {
            if (index < notificationController.notifications.length) {
              // Display a notification item
              final notification = notificationController.notifications[index];
              return ListTile(
                leading: notification.storeImageUrls.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(10.0),
                        child: Image.network(
                          '${notification.storeImageUrls.first}',
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                        ),
                      )
                    : CircleAvatar(
                        backgroundColor: AppColors.primary,
                        child: Icon(Icons.notifications, color: Colors.white),
                      ),
                title: Text(notification.title, style: TextStyle(fontWeight: AppFontWeights.bold)),
                subtitle: Text(notification.message),
               
              );
            } else {
              // Display a loading indicator at the bottom if more data is loading
              return notificationController.isLoading.isTrue
                  ? Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(child: CupertinoActivityIndicator()),
                    )
                  : SizedBox.shrink();
            }
          },
        );
      }
    });
  }
}

class NotificationListView extends StatelessWidget {
  final NotificationController notificationController;

  const NotificationListView({super.key, required this.notificationController});

  @override
  Widget build(BuildContext context) {
    return NotificationInfiniteScrollList(notificationController: notificationController);
  }
}

class NotificationInfiniteScrollList extends StatelessWidget {
  final NotificationController notificationController;
  final BadgeController badgeController = Get.find<BadgeController>();
  final ScrollController scrollController = ScrollController();

  NotificationInfiniteScrollList({super.key, required this.notificationController}) {
    // Add a listener to detect when the user has scrolled to the bottom
    scrollController.addListener(() {
      if (scrollController.position.pixels == scrollController.position.maxScrollExtent &&
          !notificationController.isLoading.value) {
        // Load more notifications when at the bottom of the list
        notificationController.loadMoreNotifications();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: scrollController,
      itemCount: notificationController.notifications.length + 1, // +1 for the loading indicator at the end
      itemBuilder: (context, index) {
        if (index < notificationController.notifications.length) {
          // Display a notification item
          final notification = notificationController.notifications[index];
          return ListTile(
            leading: notification.storeImageUrls.isNotEmpty
                ? ClipRRect(
  borderRadius: BorderRadius.circular(10.0), // Ajustez la valeur pour le rayon des coins
  child: Image.network(
    '${notification.storeImageUrls.first}',
    width: 50,
    height: 50,
    fit: BoxFit.cover, // Cela permet à l'image de s'adapter à la taille définie
  ),
) : ClipRRect(
  borderRadius: BorderRadius.circular(10.0), // Ajustez la valeur pour le rayon des coins
  child: Image.asset(
    'assets/images/icon.png',
    width: 50,
    height: 50,
    fit: BoxFit.cover, // Cela permet à l'image de s'adapter à la taille définie
  ),
),
            title: Text(notification.title,style: TextStyle(fontWeight: AppFontWeights.bold),),
            subtitle: Text(notification.message),
            trailing: notification.isRead ? null : Icon(Icons.fiber_manual_record, color: Colors.red),
            onTap: () {
              // Mark notification as read when tapped
              notificationController.markAsRead(notification.id);
            },
          );
        } else {
          // Display a loading indicator at the bottom if more data is loading
          return notificationController.isLoading.isTrue
              ? Center(child: CircularProgressIndicator())
              : SizedBox.shrink();
        }
      },
    );
  }
}







class CustomTabButton extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onPressed;
  final int badgeCount;

  const CustomTabButton({
    required this.text,
    required this.isSelected,
    required this.onPressed,
    this.badgeCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        height: 44,
        width: double.infinity, // Important pour que Expanded fonctionne
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            if (badgeCount > 0)
              Container(
                margin: EdgeInsets.only(left: 8),
                padding: EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Text(
                  badgeCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}


class ConversationPage extends StatelessWidget {
  final int itemId;
  final dynamic discussionId;
  final Map<String, dynamic>? item;
  final Map<String, dynamic>? user;

  final ConversationController conversationController = Get.put(ConversationController());
  final TextEditingController offerController = TextEditingController();
  final String interlocutor;
  final bool isStoreDiscussion;

  ConversationPage({
    Key? key,
    this.user,
    required this.itemId,
    required this.item,
    required this.discussionId,
    required this.interlocutor,
    this.isStoreDiscussion = false,
  }) : super(key: key);

  // Function to make a phone call
  void _makePhoneCall(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      Get.snackbar(
        'Error',
        'Could not launch phone call',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  final ScrollController scrollController = ScrollController();

    void scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
Widget build(BuildContext context) {
  conversationController.messages.listen((_) {
    // Scroll to bottom when messages are updated
    scrollToBottom();
  });

  // Load discussion details and scroll to bottom
  conversationController.loadDiscussionDetails(discussionId).then((_) {
    scrollToBottom();
  });

  String imgurl = item != null && item!['images'][0]['value'] != null
      ? '${item!['images'][0]['value']}'
      : '${item!['images'][0]['url']}';



  bool userIsTheOwner = item != null && item!['user_id'] == Get.find<AuthController>().user!.id;



  return Scaffold(
    appBar: AppBar(
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(interlocutor),
            IconButton(
          icon: Icon(Icons.phone, color: AppColors.primary, size: 20),
          onPressed: () => _makePhoneCall(user?['phone']),
        ),
        ],
      ),
    ),
    body: Column(
      children: [
        if (item != null) // Display item details only if provided
          Container(
            padding: EdgeInsets.all(16),
            color: Theme.of(context).dividerColor,
            child: Row(
              children: [
                Image.network(
                  imgurl,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item!['title'],
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text("${item!['price']} mru"),
                    ],
                  ),
                ),
                if (!userIsTheOwner && !isStoreDiscussion)
                  ElevatedButton(
                    onPressed: () {
                      conversationController.isMakingOffer.value = true;
                      conversationController.textController.clear();
                      FocusScope.of(context).unfocus(); // Close the keyboard
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text("Make an offer "),
                  )
              ],
            ),
          ),

        Expanded(
          child: Obx(
            () {
              return ListView.builder(
                controller: scrollController,
                itemCount: conversationController.messages.length,
                itemBuilder: (context, index) {
                  final message = conversationController.messages[index];

                  log('message ${message}');
                  final dateCategory = message.dateCategory ?? '';

                  bool showDateHeader = index == 0 ||
                      (message.dateCategory != conversationController.messages[index - 1].dateCategory);

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (showDateHeader)
                        Center(
                          child: Container(
                            margin: EdgeInsets.symmetric(vertical: 8.0),
                            padding: EdgeInsets.symmetric(vertical: 6.0, horizontal: 16.0),
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              dateCategory,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ),

                      message.isOffer
                        ? OfferBubble(message: message, itemName: item!['title'])
                        : MessageBubble(message: message)
                    ],
                  );
                },
              );
            },
          ),
        ),

        Obx(() {
          final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom > 0;
          return Padding(
            padding: EdgeInsets.only(
              left: 16.0,
              right: 8.0,
              bottom: isKeyboardOpen ? 8.0 : 34.0,
            ),
            child: Row(
              children: [
                Expanded(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: 150.0,
                    ),
                    child: Scrollbar(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        reverse: true,
                        child: TextField(
                          controller: conversationController.textController,
                          maxLines: 4,
                          minLines: 1,
                          keyboardType: conversationController.isMakingOffer.value
                              ? TextInputType.number
                              : TextInputType.text,
                          onChanged: (value) {
                            conversationController.newMessage.value = value;
                          },
                          decoration: InputDecoration(
                            hintText: conversationController.isMakingOffer.value
                                ? 'Enter your offer'
                                : 'Type a message',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send),
                  onPressed: () {
                    if (conversationController.isMakingOffer.value) {
                      final offerValue = double.tryParse(conversationController.newMessage.value) ?? 0.0;
                      if (offerValue > 0) {
                        conversationController.offerAmount.value = offerValue;
                        conversationController.addOfferMessage(
                          discussionId,
                          offerValue,
                        );
                        conversationController.textController.clear();
                        conversationController.isMakingOffer.value = false;
                        FocusScope.of(context).unfocus();
                      } else {
                        // Get.snakbar("Invalid Offer", "Please enter a valid offer amount.");
                      }
                    } else {
                      conversationController.sendMessage(discussionId, false, null , isStoreDiscussion);
                    }
                  },
                )
              ],
            ),
          );
        }),
      ],
    ),
  );
}

  String _getDefaultDateCategory(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateToCheck = DateTime(date.year, date.month, date.day);

    if (dateToCheck == today) {
      return 'Today';
    } else if (dateToCheck == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat('MMMM d, y').format(date);
    }
  }
}

class MessageBubble extends StatelessWidget {
  final Message message;

  MessageBubble({required this.message});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: message.sentByMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Align(
          alignment: message.sentByMe ? Alignment.centerRight : Alignment.centerLeft,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75, // Limite la largeur de la bulle à 75% de la largeur de l'écran
            ),
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 14),
              decoration: BoxDecoration(
                color: message.sentByMe ? AppColors.primary : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                message.content ?? '',
                style: TextStyle(
                  color: message.sentByMe ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            message.formattedTime,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontSize: 10,
            ),
          ),
        ),
      ],
    );
  }
}

class OfferBubble extends StatelessWidget {
  final Message message;
  final String itemName;
  OfferBubble({required this.message, required this.itemName});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: message.sentByMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Align(
          alignment: message.sentByMe ? Alignment.centerRight : Alignment.centerLeft,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75, // Limite la largeur de la bulle à 75% de la largeur de l'écran
            ),
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 14),
              decoration: BoxDecoration(
                color:  Color(0xFFF07A13),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                         message.sentByMe
                          ? 'You made an offer:'
                          : 'You received an offer of',
                        style: const  TextStyle(
                          color: Colors.white,
                        ),
                      ),
                       Text(
                       " ${message.price!.toStringAsFixed(0)} ",
                        style: const  TextStyle(
                          color: Colors.black,
                        fontWeight: AppFontWeights.bold,
                        ),
                       ),
                        Text(
                        'MRU for',
                        style: TextStyle(
                          color: Colors.white,
                        ),
                       ),
                    ],
                  ),
                  Text(
                 itemName,
                style: const  TextStyle(
                  color: Colors.black,
                  fontWeight: AppFontWeights.bold,
                ),
              ),
                ],
              ),

            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            message.formattedTime,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontSize: 10,
            ),
          ),
        ),
      ],
    );
  }
}

