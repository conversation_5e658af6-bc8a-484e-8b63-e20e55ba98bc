import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/profil/delete_account_page.dart';
import 'package:boutigak/views/profil/my_information_page.dart';
import 'package:boutigak/views/profil/password_page.dart';
import 'package:boutigak/views/profil/profil_page.dart';
import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class SettingsPage extends StatelessWidget {
  final authController = Get.find<AuthController>();

  SettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget settingTile(String title, IconData icon, Widget page) {
      return Container(
        margin: EdgeInsets.only(
          bottom: 8.h,
          left: 8.w,
          right: 8.w,
        ),
        decoration: BoxDecoration(
          //  border: Border.all(color: AppColors.disabled, width: 1), // Bordure blanche de 1px
          // borderRadius: BorderRadius.circular(8.r), // Border radius
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 2,
              offset: Offset(0.2, 0.2), // Position de l'ombre
            ),
          ],
        ),
        child: Material(
          color: AppColors.surface,
          child: InkWell(
            borderRadius: BorderRadius.circular(12.r),
            onTap: () {
              Get.to(page);
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              child: Row(
                children: [
                  FaIcon(icon, size: 20.sp, color: AppColors.primary),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(fontSize: 14.sp, color: Colors.black),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.sp,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('settings'.tr, style: TextStyle(fontSize: 18.sp)),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.primary,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 8.h,
            ),
            settingTile(
              'user_name'.tr,
              FontAwesomeIcons.solidUser,
              MyInformationPage(
                firstName: authController.user?.firstName ?? '',
                lastName: authController.user?.lastName ?? '',
              ),
            ),
            settingTile(
              'language'.tr,
              FontAwesomeIcons.language,
              LanguageSelector(),
            ),
            settingTile(
              'my_addresses'.tr,
              FontAwesomeIcons.locationDot,
              AddressManagementPage(),
            ),
            settingTile(
              'password'.tr,
              FontAwesomeIcons.lock,
              PasswordPage(),
            ),
            settingTile(
              'delete_account'.tr,
              FontAwesomeIcons.deleteLeft,
              DeleteAccountPage(),
            ),
            SizedBox(height: 30.h),
          ],
        ),
      ),
    );
  }
}
