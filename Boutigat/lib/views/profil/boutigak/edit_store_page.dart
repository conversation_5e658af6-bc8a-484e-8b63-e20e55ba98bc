import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';

class EditStorePage extends StatefulWidget {
  const EditStorePage({Key? key}) : super(key: key);

  @override
  State<EditStorePage> createState() => _EditStorePageState();
}

class _EditStorePageState extends State<EditStorePage> {
  final StoreController storeController = Get.find<StoreController>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController fromDateController = TextEditingController();
  final TextEditingController toDateController = TextEditingController();
  TimeOfDay? openingTime;
  TimeOfDay? closingTime;
  bool isTimePickerOpen = false;
  File? selectedImageFile; // Add this to track the selected image file

  @override
  void initState() {
    super.initState();
    // Fetch store types if not already loaded
    if (storeController.storeTypes.isEmpty) {
      storeController.fetchStoreTypes();
    }
    
    // Pre-populate fields with current store data
    if (storeController.myStore.value != null) {
      storeController.name.value = storeController.myStore.value!.name;
      storeController.description.value = storeController.myStore.value!.description ?? '';
      storeController.type.value = storeController.myStore.value!.typeId.toString();
      storeController.openingHours.value = storeController.myStore.value!.openingTime ?? "09:00";
      storeController.closingHours.value = storeController.myStore.value!.closingTime ?? "21:00";
      
      // Initialize controllers with current values
      nameController.text = storeController.name.value;
      descriptionController.text = storeController.description.value;
    }
  }

  @override
  void dispose() {
    // Dispose controllers to prevent memory leaks
    nameController.dispose();
    descriptionController.dispose();
    fromDateController.dispose();
    toDateController.dispose();
    super.dispose();
  }

  InputDecoration inputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(15),
      ),
    );
  }

  Future<void> _selectTime(BuildContext context, bool isOpeningTime) async {
    final TimeOfDay initialTime = isOpeningTime
        ? TimeOfDay.fromDateTime(DateTime.now().add(const Duration(hours: 9)))
        : TimeOfDay.fromDateTime(DateTime.now().add(const Duration(hours: 21)));

    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (pickedTime != null) {
      setState(() {
        if (isOpeningTime) {
          openingTime = pickedTime;
          storeController.openingHours.value = 
              '${pickedTime.hour.toString().padLeft(2, '0')}:${pickedTime.minute.toString().padLeft(2, '0')}';
        } else {
          closingTime = pickedTime;
          storeController.closingHours.value = 
              '${pickedTime.hour.toString().padLeft(2, '0')}:${pickedTime.minute.toString().padLeft(2, '0')}';
        }
      });
    }
  }
    void showLoadingDialog(BuildContext context) {
  showDialog(
    barrierDismissible: false,
    context: context,
    builder: (context) => Container(
      color: Colors.black.withOpacity(0.4),
      child: Center(
        child: CupertinoTheme(
          data: const CupertinoThemeData(
            brightness: Brightness.dark,
          ),
          child: const CupertinoActivityIndicator(radius: 15),
        ),
      ),
    ),
  );
}
@override
Widget build(BuildContext context) {
  return Scaffold(
    appBar: AppBar(
      title:  Text('edit'.tr +" "+"store".tr),
    ),
    body: Obx(() {
      if (storeController.isLoading.isTrue && storeController.storeTypes.isEmpty) {
        return const Center(child: CircularProgressIndicator());
      }

      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(15),
                      child: Container(
                        width: 200,
                        height: 80,
                        color: Colors.grey[300],
                        child: selectedImageFile != null
                            ? Image.file(selectedImageFile!, fit: BoxFit.cover)
                            : (storeController.myStore.value != null &&
                                    storeController.myStore.value!.images.isNotEmpty)
                                ? Image.network(
                                    '${storeController.myStore.value!.images.first}',
                                    fit: BoxFit.cover)
                                : const Icon(Icons.store, size: 40, color: AppColors.primary),
                      ),
                    ),
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        color: Colors.black.withOpacity(0.3),
                        child: IconButton(
                          icon: const Icon(Icons.add_a_photo, color: Colors.white),
                          onPressed: () async {
                            final picker = ImagePicker();
                            final pickedFile =
                                await picker.pickImage(source: ImageSource.gallery);
                            if (pickedFile != null) {
                              setState(() {
                                selectedImageFile = File(pickedFile.path);
                                storeController.boutiquePictures.clear();
                                storeController.addBoutiquePicture(pickedFile.path);
                              });
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('store_name'.tr,
                          style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.primary)),
                      const SizedBox(height: 4),
                      TextField(
                        controller: nameController,
                        decoration: inputDecoration(""),
                        onChanged: (value) => storeController.name.value = value,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('store_description'.tr,
                style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.primary)),
            const SizedBox(height: 8),
            TextField(
              controller: descriptionController,
              decoration: inputDecoration(""),
              maxLines: 5,
              textInputAction: TextInputAction.done,
              onEditingComplete: () => FocusScope.of(context).unfocus(),
              onChanged: (value) => storeController.description.value = value,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              decoration: inputDecoration('store_type'.tr),
              value: storeController.type.value.isNotEmpty ? storeController.type.value : null,
              items: storeController.storeTypes.map((type) {
                return DropdownMenuItem<String>(
                  value: type.id.toString(),
                  child: Text(type.name),
                );
              }).toList(),
              onChanged: (value) => storeController.type.value = value ?? '',
            ),
            const SizedBox(height: 16),
            ListTile(
              title:  Text("opening_time".tr),
              trailing: Text(storeController.openingHours.value),
              onTap: () => _selectTime(context, true),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
                side: BorderSide(color: Theme.of(context).dividerColor),
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: Text('closing_time'.tr),
              trailing: Text(storeController.closingHours.value),
              onTap: () => _selectTime(context, false),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
                side: BorderSide(color: Theme.of(context).dividerColor),
              ),
            ),
            const SizedBox(height: 16),
            CustomButton(
              text: "edit".tr,
              onPressed: () async {
                  showLoadingDialog(context); // <- Ajout du loader

    await storeController.updateStore();

    Navigator.of(context).pop(); // Fermer le loader

    Get.back(); // Retour

                Get.back();
              
              },
            )
          ],
        ),
      );
    }),
  );
}

}
