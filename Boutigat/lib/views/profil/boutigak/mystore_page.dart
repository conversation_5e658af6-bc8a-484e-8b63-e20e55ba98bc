import 'dart:io';
import 'dart:ui';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/boutigat_store/orderdeatails_page.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/sell/sell_widgets.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/upload_progress_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'package:palette_generator/palette_generator.dart';





class StoreProductPage extends StatefulWidget {
  @override
  _StoreProductPageState createState() => _StoreProductPageState();
}

class _StoreProductPageState extends State<StoreProductPage> {
  final StoreController storeController = Get.put(StoreController());
  final double expandedHeight = 300.0;
  Color? dominantColor = Colors.blue; // Initial default color

  @override
  void initState() {
    super.initState();
    // Fetch categories and store information
    storeController.fetchMyFavoriteCategories();
    storeController.fetchedMyStoreInformation();
  }

  bool isDark(Color color) {
    double luminance = (0.299 * color.red + 0.587 * color.green + 0.114 * color.blue) / 255;
    return luminance < 0.5; // Si la luminance est inférieure à 0.5, la couleur est foncée
  }

  Future<void> _updateDominantColor(String imageUrl) async {
    final PaletteGenerator generator = await PaletteGenerator.fromImageProvider(
      NetworkImage(imageUrl),
      size: Size(200, 100),
    );
    setState(() {
      dominantColor = generator.dominantColor?.color ?? AppColors.primary;
    });
  }

  @override
Widget build(BuildContext context) {
  return Scaffold(
    body: Obx(() {
      final store = storeController.myStore.value;

      if (store != null && store.images.isNotEmpty) {
        final imageUrl = '${store.images.first}';

        // Vérifiez si l'image a changé avant de recalculer la couleur dominante
        if (dominantColor == Colors.blue) {
          // Si dominantColor est encore à sa valeur par défaut, on la calcule
          _updateDominantColor(imageUrl);
        }

        final textColor = (dominantColor != null && isDark(dominantColor!)) ? Colors.white : Colors.black;

        return NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return <Widget>[
              SliverAppBar(
                expandedHeight: expandedHeight,
                pinned: true,
                floating: true,
                elevation: 0,
                backgroundColor: AppColors.surface,
                flexibleSpace: FlexibleSpaceBar(
                  collapseMode: CollapseMode.pin,
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      Image(
                        image: NetworkImage(imageUrl),
                        fit: BoxFit.cover,
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.1),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {},
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      foregroundColor: AppColors.surface,
                    ),
                    child: Text(store.followersCount.toString() + " Followers"),
                  ),
                  IconButton(
                    icon: Icon(Icons.search, color: Theme.of(context).colorScheme.surface),
                    onPressed: () {},
                  ),
                  IconButton(
                    icon: Icon(Icons.list, color: AppColors.background),
                    onPressed: () {},
                  ),
                ],
              ),
              SliverPersistentHeader(
                delegate: _SliverAppBarDelegate(
                  child: FavoriteCategoryListWidget(
                    // controller: storeController,
                    storeId: store.id!,
                  ),
                  height: 62,
                ),
                pinned: true,
              ),
            ];
          },
          body: Container(
            color: CupertinoColors.systemGroupedBackground,
            child: Column(
              children: [
                Expanded(
                  child: storeController.selectedCategory.value != null
                      ? MyStoreitemsListViewWidget(
                          category: storeController.selectedCategory.value!,
                          items: storeController.myItems
                              .where((item) => item.categoryId == storeController.selectedCategory.value!.id)
                              .toList(),
                              storeImage:'${store.images.first}'
                        )
                      : Center(
                          child: Text("Select a category to view items"),
                        ),
                ),
              ],
            ),
          ),
        );
      } else {
        return Center(child: CircularProgressIndicator());
      }
    }),
    floatingActionButton: FloatingActionButton(
      onPressed: () {
        Get.to(() => SellStorePage());
      },
      child: Icon(Icons.add, color: AppColors.surface),
      backgroundColor: dominantColor,
      tooltip: 'Ajouter un item',
    ),
    floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
  );
}

}


class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _SliverAppBarDelegate({required this.child, required this.height});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return oldDelegate.child != child || oldDelegate.height != height;
  }
}











class FavoriteCategoryListWidget extends StatefulWidget {
  final int storeId;

  const FavoriteCategoryListWidget({
    Key? key, 
    required this.storeId,
  }) : super(key: key);

  @override
  _FavoriteCategoryListWidgetState createState() => _FavoriteCategoryListWidgetState();
}

class _FavoriteCategoryListWidgetState extends State<FavoriteCategoryListWidget> {
  final StoreController storeController = Get.find<StoreController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      storeController.fetchStoreFavoriteCategories(widget.storeId.toString());
    });
  }

  @override
 @override
Widget build(BuildContext context) {
  return Container(
    height: 60,
    color: Theme.of(context).colorScheme.surface,
    child: Obx(() {
      final categories = storeController.FavoriteCategories;

      // ✅ Si aucune catégorie, on affiche seulement le bouton +
      if (categories.isEmpty) {
        return ListView(
          scrollDirection: Axis.horizontal,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Container(
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.add, color: Colors.black),
                  onPressed: () {
                    Get.to(() => CategorySelectionPage(controller: storeController));
                  },
                ),
              ),
            ),
          ],
        );
      }

      // ✅ Sinon, on affiche la liste + le bouton + à la fin
      return ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length + 1, // +1 pour le bouton +
        itemBuilder: (context, index) {
          if (index == categories.length) {
            // ✅ Dernier index = bouton +
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Container(
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.add, color: Colors.black),
                  onPressed: () {
                    Get.to(() => CategorySelectionPage(controller: storeController));
                  },
                ),
              ),
            );
          }

          final category = categories[index];
          final isSelected = storeController.selectedCategory.value == category;

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: TextButton(
              onPressed: () {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  storeController.selectCategory(
                    category,
                    widget.storeId.toString(),
                  );
                });
              },
              onLongPress: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      title: const Text('Supprimer la catégorie'),
                      content: const Text(
                          'Êtes-vous sûr de vouloir supprimer cette catégorie de vos favoris ?'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Annuler'),
                        ),
                        TextButton(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            storeController.deleteStoreFavoriteCategory(category.id);
                            setState(() {});
                          },
                          child: const Text(
                            'Supprimer',
                            style: TextStyle(color: Colors.red),
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.onSurface,
              ),
              child: Text(
                category.getTitle(),
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          );
        },
      );
    }),
  );
}

}


class SellStorePage extends StatefulWidget {
  final int? itemId;

  const SellStorePage({Key? key, this.itemId}) : super(key: key);

  @override
  _SellStorePageState createState() => _SellStorePageState();
}

class _SellStorePageState extends State<SellStorePage> {
  final ItemController itemController = Get.put(ItemController(), permanent: true);
  final PhotoActionsController photoController =
      Get.put(PhotoActionsController(Get.find<ItemController>()), permanent: true);

  final RxString uploadStatus = "Preparing upload...".obs;
  final RxDouble uploadProgress = 0.0.obs;

  @override
  void initState() {
    super.initState();
    if (widget.itemId != null) {
      itemController.loadItemForEdit(widget.itemId!);
    }
  }

  @override
  void dispose() {
    itemController.clearItemData();
    photoController.clearPhotoActionData();
    itemController.clearFields();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
        title: Text(
          "add item to your store",
          style: TextStyle(
            color: Theme.of(context).colorScheme.surface,
            fontSize: AppTextSizes.heading,
          ),
        ),
        elevation: 0,
        backgroundColor: AppColors.primary,
      ),
      backgroundColor: Theme.of(context).colorScheme.background,
      body: SingleChildScrollView(
        child: Column(
          children: [
            PhotoActionsWidget(),
            SizedBox(height: 20),
             ItemDetailsEntryMainLanguage(),
         SizedBox(height: 20),
          ItemDetailsEntryArabic(),
            SizedBox(height: 20),
            ItemDetailsFormWidget(),
            Obx(() {
              if (itemController.selectedCategoryDetails.isEmpty) {
                return const SizedBox.shrink();
              }
              return CategoryDetailsWidget();
            }),
            Obx(() {
              return Column(
                children: [
                  if (uploadProgress.value > 0 && uploadProgress.value < 1)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: UploadProgressWidget(
                        progress: uploadProgress.value,
                        status: uploadStatus.value,
                      ),
                    ),
                  SizedBox(height: 10),
                  CustomButton(
                    text: widget.itemId != null ? "update_item".tr : "upload_item".tr,
                    onPressed: () async {
                      uploadProgress.value = 0.0;
                      uploadStatus.value = "Preparing upload...";

                      if (widget.itemId != null) {
                        await itemController.updateItem(widget.itemId!);
                        itemController.clearItemData();
                        photoController.clearPhotoActionData();
                        itemController.clearFields();

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text("item_updated_successfully".tr)),
                        );
                        await Future.delayed(Duration(seconds: 1));
                        uploadProgress.value = 0.0;
                        uploadStatus.value = "";
                        Get.find<StoreController>().fetchMyStoreItems();
                        Get.back();
                      } else {
                        try {
                          uploadProgress.value = 0.25;
                          uploadStatus.value = "Validating data...";
                          await Future.delayed(Duration(milliseconds: 500));

                          uploadProgress.value = 0.5;
                          uploadStatus.value = "Uploading images...";

                          uploadProgress.value = 0.75;
                          uploadStatus.value = "Creating item...";

                          await itemController.postStoreItemWithProgress((progress) {
                            uploadProgress.value = 0.75 + (progress * 0.25);
                          });

                          uploadProgress.value = 1.0;
                          uploadStatus.value = "Upload complete!";

                          if (itemController.isItemUploaded.value) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text("item_uploaded_successfully".tr)),
                            );
                            itemController.clearItemData();
                            photoController.clearPhotoActionData();
                            itemController.clearFields();

                            await Future.delayed(Duration(seconds: 1));
                            uploadProgress.value = 0.0;
                            uploadStatus.value = "";
                          }
                        } catch (e) {
                          uploadStatus.value = "Upload failed. Please try again.";
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text("Error uploading item: ${e.toString()}")),
                          );
                        }
                      }
                    },
                  ),
                ],
              );
            }),
            SizedBox(height: 30),
          ],
        ),
      ),
    );
  }
}


class CategorySelectionPage extends StatelessWidget {
  final StoreController controller;

  CategorySelectionPage({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Charger les catégories disponibles via le StoreController
    controller.fetchCategories(); 
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Category'),
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator()); // Affiche un loader pendant le chargement
        }
        if (controller.categories.isEmpty) {
          return Center(child: Text('No categories available.'));
        }
        return ListView.builder(
          itemCount: controller.categories.length,
          itemBuilder: (context, index) {
            Category category = controller.categories[index];
            return ListTile(
              title: Text(category.titleEn), // Affiche le nom de la catégorie
              onTap: () {
                controller.addFavoriteCategory(category.id); // Ajoute la catégorie sélectionnée aux favorites
                Get.back(); // Retour à la page précédente après sélection
              },
            );
          },
        );
      }),
    );
  }
}
